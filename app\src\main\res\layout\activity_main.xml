<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF"
    android:orientation="vertical"
    tools:ignore="TextFields,HardcodedText">

    <Spinner
        android:id="@+id/spinner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:drawable/btn_dropdown"
        android:spinnerMode="dropdown" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_getData"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:text="قراءة" />

        <Space
            android:layout_width="50dp"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <Spinner
                android:id="@+id/spinner_meterCo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:drawable/btn_dropdown"
                android:spinnerMode="dropdown" />

            <Spinner
                android:id="@+id/spinner_globalVersion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:drawable/btn_dropdown"
                android:spinnerMode="dropdown"
                android:visibility="gone" />
        </LinearLayout>
    </LinearLayout>


    <shoaa.smartmeterreadertester.NumberProgressBar
        android:id="@+id/progress_circle"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_gravity="center"
        android:visibility="gone"
        app:currentValue="0"
        app:numberTextSize="10sp"
        app:progressBarShape="circle"
        app:reachedBarHeight="1.5dp"
        app:textOffset="3dp"
        app:unreachedBarColor="#CCCCCC"
        app:unreachedBarHeight="0.75dp" />


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp">

        <TextView
            android:id="@+id/txt_data"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />
    </ScrollView>

    <Space
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <Button
        android:id="@+id/btn_send"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center|bottom"
        android:text="ارسال" />
</LinearLayout>