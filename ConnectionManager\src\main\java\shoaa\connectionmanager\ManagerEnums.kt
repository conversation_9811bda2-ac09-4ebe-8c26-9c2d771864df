package shoaa.connectionmanager

enum class BaudRate {
    BAUD_9600_8_N_1,
    BAUD_4800_8_N_1,
    BAUD_2400_7_E_1,
    BAUD_300_7_E_1,
    BT_BAUD_R_9600_8_N_1,
    BT_BAUD_N_9600_8_N_1,
    BT_BAUD_N_4800_8_N_1,
    BT_BAUD_N_2400_7_E_1,
    BT_BAUD_N_300_7_E_1,
    BT_BAUD_M_9600_8_N_1,
    BT_BAUD_M_4800_8_N_1,
    BT_BAUD_M_2400_7_E_1,
    BT_BAUD_M_300_7_E_1;


    fun getBaud(): Int {
        return if (this == BAUD_9600_8_N_1 || this == BT_BAUD_R_9600_8_N_1 || this == BT_BAUD_N_9600_8_N_1 || this == BT_BAUD_M_9600_8_N_1) {
            9600
        } else if (this == BAUD_4800_8_N_1 || this == BT_BAUD_N_4800_8_N_1 || this == BT_BAUD_M_4800_8_N_1) {
            4800
        } else if (this == BAUD_2400_7_E_1 || this == BT_BAUD_N_2400_7_E_1 || this == BT_BAUD_M_2400_7_E_1) {
            2400
        } else if (this == BAUD_300_7_E_1 || this == BT_BAUD_N_300_7_E_1 || this == BT_BAUD_M_300_7_E_1) {
            300
        } else {
            0
        }
    }

    fun dataBit(): Int {
        return if (this == BAUD_9600_8_N_1 || this == BT_BAUD_R_9600_8_N_1 || this == BT_BAUD_N_9600_8_N_1 || this == BT_BAUD_M_9600_8_N_1) {
            8
        } else if (this == BAUD_4800_8_N_1 || this == BT_BAUD_N_4800_8_N_1 || this == BT_BAUD_M_4800_8_N_1) {
            8
        } else if (this == BAUD_2400_7_E_1 || this == BT_BAUD_N_2400_7_E_1 || this == BT_BAUD_M_2400_7_E_1) {
            7
        } else if (this == BAUD_300_7_E_1 || this == BT_BAUD_N_300_7_E_1 || this == BT_BAUD_M_300_7_E_1) {
            7
        } else {
            0
        }
    }

    fun stopBit(): Int {
        return if (this == BAUD_9600_8_N_1 || this == BT_BAUD_R_9600_8_N_1 || this == BT_BAUD_N_9600_8_N_1 || this == BT_BAUD_M_9600_8_N_1) {
            1
        } else if (this == BAUD_4800_8_N_1 || this == BT_BAUD_N_4800_8_N_1 || this == BT_BAUD_M_4800_8_N_1) {
            1
        } else if (this == BAUD_2400_7_E_1 || this == BT_BAUD_N_2400_7_E_1 || this == BT_BAUD_M_2400_7_E_1) {
            1
        } else if (this == BAUD_300_7_E_1 || this == BT_BAUD_N_300_7_E_1 || this == BT_BAUD_M_300_7_E_1) {
            1
        } else {
            0
        }
    }

    fun parity(): Int {
//        /** No parity.  */
//        val PARITY_NONE = 0
//
//        /** Odd parity.  */
//        val PARITY_ODD = 1
//
//        /** Even parity.  */
//        val PARITY_EVEN = 2
//
//        /** Mark parity.  */
//        val PARITY_MARK = 3
//
//        /** Space parity.  */
//        val PARITY_SPACE = 4
        return if (this == BAUD_9600_8_N_1 || this == BT_BAUD_R_9600_8_N_1 || this == BT_BAUD_N_9600_8_N_1 || this == BT_BAUD_M_9600_8_N_1) {
            0
        } else if (this == BAUD_4800_8_N_1 || this == BT_BAUD_N_4800_8_N_1 || this == BT_BAUD_M_4800_8_N_1) {
            0
        } else if (this == BAUD_2400_7_E_1 || this == BT_BAUD_N_2400_7_E_1 || this == BT_BAUD_M_2400_7_E_1) {
            2
        } else if (this == BAUD_300_7_E_1 || this == BT_BAUD_N_300_7_E_1 || this == BT_BAUD_M_300_7_E_1) {
            2
        } else {
            0
        }
    }
}

enum class Connected {
    False,
    Pending,
    True
}

enum class ConnectionType {
    BLUETOOTH,
    USB,
}
