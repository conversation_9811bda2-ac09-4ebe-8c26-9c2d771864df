package shoaa.smartmeterreadertester;

import android.app.Application;
import android.content.Context;

import androidx.multidex.MultiDex;

public class MyApp extends Application {
    private static MyApp instance;

    public static MyApp getInstance() {
        return instance;
    }

    public static Context getContext() {
        return instance;
        // or return instance.getApplicationContext();
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(LocaleManager.updateResources(base, "en"));
        MultiDex.install(this);
    }

    @Override
    public void onCreate() {
        instance = this;
        super.onCreate();
    }
}