package shoaa.globalreader;

import static shoaa.common.Utils.writeStringAsFile;

import android.content.Context;

import com.google.gson.Gson;

import java.util.HashMap;
import java.util.Locale;

import shoaa.common.ProgressCallback;
import shoaa.connectionmanager.BaudRate;
import shoaa.connectionmanager.Common;
import shoaa.connectionmanager.ConnectionManager;
import shoaa.smartmeterreader.ReadingResponse;
import shoaa.smartmeterreader.ReadingResult;
import shoaa.connectionmanager.ConnectionType;

import java.util.Timer;

/**
 * Created by Islam Darwish
 */

public class GlobalReader {
    static GlobalVersion currentVersion = GlobalVersion.VERSION_2019;
    static String session = "";
    private static int progress = 0;
    private static ProgressCallback progressCallback;
    private static int sendTimeOut = Common.TIME_OUT_1500;

    public static ReadingResponse read(Context context, GlobalVersion globalVersion, ProgressCallback mProgressCallback) {
        session = "";
        HashMap<GlobalVersion, String> meterDataMap = null;
        int tryCount = 0;
        progressCallback = mProgressCallback;
        GlobalResponse readingResponse = new GlobalResponse();
        readingResponse.readingResult = ReadingResult.CAN_NOT_CONNECT;

        if (!ConnectionManager.Companion.getInstance().isConnected()) {
            if (ConnectionManager.Companion.getInstance().connectAsync(context, 5000) != 0) {
                readingResponse = new GlobalResponse();
                readingResponse.readingResult = ReadingResult.CAN_NOT_CONNECT;
                readingResponse.message = "";
                return readingResponse;
            }
        }
        if (ConnectionManager.Companion.getInstance().isConnected()) {
            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
            while (meterDataMap == null && tryCount < 5 && readingResponse.readingResult != ReadingResult.SUCCESS) {
                progress = 5;
                progressCallback.update(progress);
                tryCount++;
                if (globalVersion != null) {
                    meterDataMap = readMeter(globalVersion);
                } else {
                    meterDataMap = readMeter();
                }
                Response response;
                Request requestData = new Request();
                if (meterDataMap != null) {
                    GlobalVersion readedGLobalVersion = (GlobalVersion) meterDataMap.keySet().toArray()[0];
                    switch (readedGLobalVersion) {
                        case VERSION_2019:
                            requestData.service = "service_2019";
                            break;
                        case VERSION_2016:
                            requestData.service = "service_2016";
                            break;
                        case VERSION_2013:
                            requestData.service = "service_2013";
                            break;
                        case VERSION_2008:
                            requestData.service = "service_2008";
                            break;
                    }
                    requestData.pdu = (String) meterDataMap.values().toArray()[0];
                    try {
                        String json = new Gson().toJson(requestData);
                        String data = new ShoaaProtocolHelper().handle(json);
                        response = new Gson().fromJson(data, Response.class);
                        if (response.result.equalsIgnoreCase("error")) {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }
                        response.pdu = requestData.pdu;
                        if (!response.result.equalsIgnoreCase("error")) {
                            MeterData meterData = new Gson().fromJson(response.value, MeterData.class);
                            if (isTrusted(requestData.pdu, currentVersion, meterData)) {
                                progress = 100;
                                progressCallback.update(progress);
                                readingResponse.fromJson(response.value);
                                readingResponse.readingResult = ReadingResult.SUCCESS;
                                readingResponse.message = "";
                                break;
                            }
                        } else {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }
                        try {
                            Thread.sleep(1000);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        if (readingResponse.readingResult == ReadingResult.SUCCESS) {
                            writeStringAsFile(context, session, "session.txt");
                            return readingResponse;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        readingResponse.readingResult = ReadingResult.OTHER;
                        readingResponse.message = e.getMessage();
                        writeStringAsFile(context, session, "session.txt");
                        return readingResponse;
                    }
                }
            }
        }
        writeStringAsFile(context, session, "session.txt");
        return readingResponse;
    }

    public static ReadingResponse read(Context context, String barcode, ProgressCallback mProgressCallback) {
        session = "";
        progress = 0;
        progressCallback = mProgressCallback;
        GlobalResponse readingResponse = new GlobalResponse();
        readingResponse.message = "";
        GlobalVersion[] globalVersions;
        if ((barcode.length() == 8) && (barcode.startsWith("2026") || barcode.startsWith("2025") || barcode.startsWith("2024") || barcode.startsWith("2023") || barcode.startsWith("2022") || barcode.startsWith("2021") || barcode.startsWith("2020") || barcode.startsWith("2019") || Integer.parseInt(barcode.substring(0, 3)) >= 680)) {
            globalVersions = new GlobalVersion[]{GlobalVersion.VERSION_2019, GlobalVersion.VERSION_2016, GlobalVersion.VERSION_2013, GlobalVersion.VERSION_2008};
        } else if ((barcode.length() == 8) && (Integer.parseInt(barcode.substring(0, 3)) < 680 && Integer.parseInt(barcode.substring(0, 3)) >= 410)) {
            if (Integer.parseInt(barcode.substring(0, 3)) > 570)
                globalVersions = new GlobalVersion[]{GlobalVersion.VERSION_2016, GlobalVersion.VERSION_2019, GlobalVersion.VERSION_2008, GlobalVersion.VERSION_2013};
            else
                globalVersions = new GlobalVersion[]{GlobalVersion.VERSION_2016, GlobalVersion.VERSION_2008, GlobalVersion.VERSION_2019, GlobalVersion.VERSION_2013};
        } else if ((barcode.length() == 8) && (Integer.parseInt(barcode.substring(0, 3)) < 410 && Integer.parseInt(barcode.substring(0, 3)) >= 0)) {
            globalVersions = new GlobalVersion[]{GlobalVersion.VERSION_2008, GlobalVersion.VERSION_2013, GlobalVersion.VERSION_2016, GlobalVersion.VERSION_2019};
        } else {
            globalVersions = new GlobalVersion[]{GlobalVersion.VERSION_2008, GlobalVersion.VERSION_2019, GlobalVersion.VERSION_2016, GlobalVersion.VERSION_2013};
        }
        readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
        if (!ConnectionManager.Companion.getInstance().isConnected()) {
            if (ConnectionManager.Companion.getInstance().connectAsync(context, 5000) != 0) {
                readingResponse = new GlobalResponse();
                readingResponse.readingResult = ReadingResult.CAN_NOT_CONNECT;
                readingResponse.message = "";
                return readingResponse;
            }
        }
        progress += 5;
        progressCallback.update(progress);
        readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;


        if (ConnectionManager.Companion.getInstance().isConnected()) {
            for (GlobalVersion globalVersion : globalVersions) {

                switch (globalVersion) {
                    case VERSION_2019:
                        currentVersion = GlobalVersion.VERSION_2019;
                        break;
                    case VERSION_2016:
                        currentVersion = GlobalVersion.VERSION_2016;
                        break;
                    case VERSION_2013:
                        currentVersion = GlobalVersion.VERSION_2013;
                        break;
                    case VERSION_2008:
                        currentVersion = GlobalVersion.VERSION_2008;
                        break;
                }
                Request requestData = readCurrentVersion();
                if (requestData != null) {
                    int tryCount = 0;
                    while (tryCount < 5) {
                        String json = new Gson().toJson(requestData);
                        String data = new ShoaaProtocolHelper().handle(json);
                        Response response = new Gson().fromJson(data, Response.class);

                        if (response != null && !response.result.equalsIgnoreCase("error")) {
                            MeterData meterData = new Gson().fromJson(response.value, MeterData.class);
                            if (requestData != null && isTrusted(requestData.pdu, currentVersion, meterData)) {
                                progress = 100;
                                progressCallback.update(progress);
                                readingResponse.fromJson(response.value);
                                readingResponse.readingResult = ReadingResult.SUCCESS;
                                readingResponse.message = "";
                                break;
                            }
                        } else {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }
                        try {
                            Thread.sleep(1000);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        tryCount++;
                        requestData = readCurrentVersion();
                    }
                    if (readingResponse.readingResult == ReadingResult.SUCCESS) {
                        writeStringAsFile(context, session, "session.txt");
                        return readingResponse;
                    }
                }
            }
        }
        writeStringAsFile(context, session, "session.txt");
        return readingResponse;
    }


    private static Request readCurrentVersion() {
        Response response = new Response();
        Request requestData = new Request();
        if (initVersionAsync(currentVersion) != 0) return null;
        switch (currentVersion) {
            case VERSION_2019:
                String res = read2019();
                if (res == null) return null;
                requestData.service = "service_2019";
                requestData.pdu = res;
                try {
                    String json = new Gson().toJson(requestData);
                    String data = new ShoaaProtocolHelper().handle(json);
                    response = new Gson().fromJson(data, Response.class);
                    if (response.result.equalsIgnoreCase("error")) {
                        return null;
                    }
                    response.pdu = requestData.pdu;
                    return requestData;
                } catch (Exception e) {
                    e.printStackTrace();
                    return null;
                }
            case VERSION_2016:
                res = read2016();
                if (res == null) return null;
                requestData = new Request();
                requestData.service = "service_2016";
                requestData.pdu = res;

                try {
                    String json = new Gson().toJson(requestData);
                    String data = new ShoaaProtocolHelper().handle(json);
                    response = new Gson().fromJson(data, Response.class);
                    if (response.result.equalsIgnoreCase("error")) {
                        return null;
                    }
                    response.pdu = requestData.pdu;
                    return requestData;
                } catch (Exception e) {
                    e.printStackTrace();
                    return null;
                }
            case VERSION_2013:
                res = read2013();
                if (res == null) return null;
                requestData = new Request();
                requestData.service = "service_2013";
                requestData.pdu = res;
                try {
                    String json = new Gson().toJson(requestData);
                    String data = new ShoaaProtocolHelper().handle(json);
                    response = new Gson().fromJson(data, Response.class);
                    if (response.result.equalsIgnoreCase("error")) {
                        return null;
                    }
                    response.pdu = requestData.pdu;
                    return requestData;
                } catch (Exception e) {
                    e.printStackTrace();
                    return null;
                }
            case VERSION_2008:
                res = read2008();
                if (res == null) return null;
                requestData = new Request();
                requestData.service = "service_2008";
                requestData.pdu = res;
                try {
                    String json = new Gson().toJson(requestData);
                    String data = new ShoaaProtocolHelper().handle(json);
                    response = new Gson().fromJson(data, Response.class);
                    if (response.result.equalsIgnoreCase("error")) {
                        return null;
                    }
                    response.pdu = requestData.pdu;
                    return requestData;
                } catch (Exception e) {
                    e.printStackTrace();
                    return null;
                }

        }
        response.pdu = requestData.pdu;
        return requestData;
    }

    public static boolean isTrusted(String frame, GlobalVersion globalVersion, MeterData meterData) {
        frame = frame.toUpperCase();
        final boolean b = (frame.contains("024D5F534E28") || frame.contains("15024D5F534E28") || frame.contains("024D5F494428")) && frame.contains("0D0A210D0A0344");
        if (!b) {
            if (globalVersion == GlobalVersion.VERSION_2019 && !(frame.startsWith("7E") && frame.endsWith("7E"))) {
                return false;
            }
            try {
                if ((globalVersion == GlobalVersion.VERSION_2019 || globalVersion == GlobalVersion.VERSION_2016) && !(Integer.parseInt(meterData.date.split("-")[0]) > 2000 && Integer.parseInt(meterData.date.split("-")[0]) < 2050)) {
                    return false;
                }
//                if (meterData.meterID.equalsIgnoreCase("0"))
//                    return false;
                if (meterData.currentMonthConsumption > Double.parseDouble(meterData.totalConsum.replaceAll("[^0-9.-]", "")))
                    return false;
                if (meterData.SliceNo > 7 || meterData.SliceNo < 1) return false;
                if (Double.parseDouble(meterData.remainMoney.replaceAll("[^0-9.-]", "")) > Double.parseDouble(meterData.TotalBalance.replaceAll("[^0-9.-]", "")))
                    return false;
                if (meterData.chargeCount > Double.parseDouble(meterData.TotalBalance.replaceAll("[^0-9.-]", "")))
                    return false;
                double monthsConsumption = meterData.consm1 + meterData.consm2 + meterData.consm3 + meterData.consm4 + meterData.consm5 + meterData.consm6 + meterData.consm7 + meterData.consm8 + meterData.consm9 + meterData.consm10 + meterData.consm11 + meterData.consm12;
                if (Double.parseDouble(meterData.totalConsum.replaceAll("[^0-9.-]", "")) < monthsConsumption)
                    return false;
            } catch (Exception ignore) {
                return false;
            }
        }
        return true;

    }

    private static String sendAsync(String hexString, int retryCount, int sendTimeOut, int readTimeOut) {
        session += "send : " + hexString + "\n";
        return ConnectionManager.Companion.getInstance().sendAsync(hexString, retryCount, sendTimeOut, readTimeOut);
    }

    private static void sendAsyncNoRes(String hexString) {
        session += "sendAsyncNoRes : " + hexString + "\n";
        ConnectionManager.Companion.getInstance().sendAsyncNoRes(hexString);
    }

    private static String read2019() {
        int tCount = 0;
        session += "2019\n";
        String res1 = sendAsync("7EA00800032193F05E7E7EA00800032193F05E7E", 1, sendTimeOut, Common.TIME_OUT_300);
        session += "res1 : " + res1 + "\n";

        if (res1.isEmpty()) return null;

        String res2 = null;
        String res3 = null;
        while (tCount < 20) {
            if (tCount > 0) {
                res1 = sendAsync("7EA00800032193F05E7E7EA00800032193F05E7E", 1, sendTimeOut, Common.TIME_OUT_300);
                session += "res1 : " + res1 + "\n";
            }
//            if (!(res1.toUpperCase(Locale.ROOT).startsWith("7E") && res1.toUpperCase(Locale.ROOT).endsWith("7E"))) {
//                tCount++;
//                continue;
//            }
//            progress += 2;
//            progressCallback.update(progress);
            res2 = sendAsync("7EA02C00032110E2A5E6E600601DA109060760857405080101BE10040E01000000065F1F0400001E1DFFFFC5E47E", 2, sendTimeOut, Common.TIME_OUT_300);
            session += "res2 : " + res2 + "\n";
//            if (!(res2.toUpperCase(Locale.ROOT).startsWith("7E") && res2.toUpperCase(Locale.ROOT).endsWith("7E"))) {
//                tCount++;
//                continue;
//            }
//            progress += 2;
//            progressCallback.update(progress);
            res3 = sendAsync("7EA01A00032132BB48E6E600C001C1000100005E140AFF020041107E", 2, sendTimeOut, Common.TIME_OUT_1000);
            session += "res3 : " + res3 + "\n";
            if ((res3.toUpperCase(Locale.ROOT).startsWith("7E") && res3.toUpperCase(Locale.ROOT).endsWith("7E"))) {
                break;
            } else {
                tCount++;
            }
        }
        progress += 2;
        progressCallback.update(progress);
        return res3;
    }


    private static String read2016() {
        session += "2016\n";
        String res1 = sendAsync("000001573102433128290307000001573102433128290307", 1, sendTimeOut, Common.TIME_OUT_1000);
        session += "res1 : " + res1 + "\n";
        if (res1.isEmpty()) return null;

        if (!(res1.toUpperCase(Locale.ROOT).contains("024D5F534E28") || res1.toUpperCase(Locale.ROOT).contains("15024D5F534E28"))) {
            progress += 2;
            progressCallback.update(progress);
            String res2 = sendAsync("000001573102433228290307", 2, sendTimeOut, Common.TIME_OUT_1000);
            session += "res2 : " + res2 + "\n";
            if (res2 == null || res2.isEmpty()) {
                sendTimeOut = Common.TIME_OUT_1500;
                res1 = sendAsync("000001573102433128290307000001573102433128290307", 1, sendTimeOut, Common.TIME_OUT_1000);
                session += "res1 : " + res1 + "\n";
                if (res1.isEmpty()) return null;
                progress += 2;
                progressCallback.update(progress);
                res2 = sendAsync("000001573102433228290307", 2, sendTimeOut, Common.TIME_OUT_1000);
                session += "res2 : " + res2 + "\n";
            }
            progress += 2;
            progressCallback.update(progress);
            if (res2.toUpperCase(Locale.ROOT).contains("024D5F534E28") || res2.toUpperCase(Locale.ROOT).contains("15024D5F534E28"))
                return res2;
            else return res1 + res2;
        }
        progress += 2;
        progressCallback.update(progress);
        return res1;
    }

    private static String read2013() {
        session += "2013\n";
//        String res1 = sendAsync("007F2F3F210D0A", 0, 500, 300);
        String res1 = sendAsync("2F3F210D0A", 0, Common.TIME_OUT_500, Common.TIME_OUT_300);
        session += "res1 : " + res1 + "\n";

        progress += 2;
        progressCallback.update(progress);
        String res2 = sendAsync("063033310D0A", 0, Common.TIME_OUT_500, Common.TIME_OUT_300);
        session += "res2 : " + res2 + "\n";

        progress += 2;
        progressCallback.update(progress);
        String res3 = sendAsync("000001573102433128290307", 2, Common.TIME_OUT_1000, Common.TIME_OUT_300);
        session += "res3 : " + res3 + "\n";
        if (!res3.isEmpty()) {
            String res4 = sendAsync("000001573102433228290307", 2, Common.TIME_OUT_1000, Common.TIME_OUT_1000);
            session += "res4 : " + res4 + "\n";
            if (res4.isEmpty()) return null;
            progress += 2;
            progressCallback.update(progress);
            if (res4.toUpperCase(Locale.ROOT).contains("024D5F534E28") || res4.toUpperCase(Locale.ROOT).contains("15024D5F534E28"))
                return res4;

            if (res3.toUpperCase(Locale.ROOT).contains("024D5F534E28") || res3.toUpperCase(Locale.ROOT).contains("15024D5F534E28"))
                return res3 + res4;

            String res7 = sendAsync("000001573102433328290307", 2, Common.TIME_OUT_1000, Common.TIME_OUT_1000);
            session += "res7 : " + res7 + "\n";
            if (res7.toUpperCase(Locale.ROOT).contains("024D5F534E28") || res7.toUpperCase(Locale.ROOT).contains("15024D5F534E28"))
                return res7 + res3 + res4;

            return res3 + res4;
        } else {
//            String res5 = sendAsync("007F2F3F210D0A", 0, 500, 300);
            String res5 = sendAsync("2F3F210D0A", 0, Common.TIME_OUT_500, Common.TIME_OUT_300);
            session += "res5 : " + res5 + "\n";
            progress += 2;
            progressCallback.update(progress);
            String res6 = sendAsync("063033310D0A", 0, Common.TIME_OUT_500, Common.TIME_OUT_300);
            session += "res6 : " + res6 + "\n";
            progress += 2;
            progressCallback.update(progress);
            String res7 = sendAsync("000001573102433328290307", 2, Common.TIME_OUT_1000, Common.TIME_OUT_1000);
            session += "res7 : " + res7 + "\n";
            if (res7.isEmpty()) return null;
            progress += 2;
            progressCallback.update(progress);
            return res7;
        }
    }

    private static String read2008() {
        session += "2008\n";
//        String res = sendAsync("007F2F3F210D0A", 0, 1500, 1000);
        String res = sendAsync("2F3F210D0A", 0, Common.TIME_OUT_1500, Common.TIME_OUT_1000);
        session += "res : " + res + "\n";

        res = res.toUpperCase();
        progress += 2;
        progressCallback.update(progress);

        String res2 = sendAsync("063033300D0A", 0, Common.TIME_OUT_1500, Common.TIME_OUT_1000);
        session += "res2 : " + res2 + "\n";
        if ((res2).isEmpty()) {
            if ((res.contains("024D5F534E28") || res.contains("15024D5F534E28") || res.contains("024D5F494428")) && res.contains("0D0A210D0A0344")) {
                return res;
            }
            return null;
        }
        res2 = res2.toUpperCase();
        progress += 2;
        progressCallback.update(progress);
        if ((res2.contains("024D5F534E28") || res2.contains("15024D5F534E28") || res2.contains("024D5F494428")) && res2.contains("0D0A210D0A0344")) {
            return res2;
        } else return res + res2;
    }

    private static HashMap<GlobalVersion, String> readMeter() {
        boolean isBluetooth = ConnectionManager.Companion.getInstance().getConnectionType() == ConnectionType.BLUETOOTH;
        boolean baudRateSet;
        
        if (isBluetooth) {
            baudRateSet = ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_R_9600_8_N_1);
        } 

        
        if (isBluetooth) {
            baudRateSet = ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_N_2400_7_E_1);
        } 
        else{
            baudRateSet=ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_2400_7_E_1);
        }

        HashMap<GlobalVersion, String> readRes = new HashMap<>();
        session += "2008\n";
        String res_2008_1 = sendAsync("007F2F3F210D0A007F2F3F210D0A", 0, Common.TIME_OUT_1000, Common.TIME_OUT_500);
        session += "res_2008_1 : " + res_2008_1 + "\n";
        progress += 1;
        progressCallback.update(progress);
        if (res_2008_1.isEmpty()) {
            res_2008_1 = sendAsync("7F2F3F210D0A7F2F3F210D0A", 0, Common.TIME_OUT_1000, Common.TIME_OUT_500);
            session += "res_2008_1 : " + res_2008_1 + "\n";
            progress += 1;
            progressCallback.update(progress);
        }
        if (!res_2008_1.isEmpty()) {
            //2008
            progress += 10;
            progressCallback.update(progress);
            String res_2008_2 = sendAsync("063033300D0A063033300D0A", 1, Common.TIME_OUT_1000, Common.TIME_OUT_500);
            session += "res_2008_2 : " + res_2008_2 + "\n";
            if (res_2008_2.isEmpty()) {
                if ((res_2008_1.contains("024D5F534E28") || res_2008_1.contains("15024D5F534E28") || res_2008_1.contains("024D5F494428")) && res_2008_1.contains("0D0A210D0A0344")) {
                    readRes.put(GlobalVersion.VERSION_2008, res_2008_1);
                    session += "final res : " + readRes.values().toArray()[0] + "\n";
                    currentVersion = GlobalVersion.VERSION_2008;
                    return readRes;
                }
            } else {
                if ((res_2008_2.contains("024D5F534E28") || res_2008_2.contains("15024D5F534E28") || res_2008_2.contains("024D5F494428")) && res_2008_2.contains("0D0A210D0A0344")) {
                    readRes.put(GlobalVersion.VERSION_2008, res_2008_2);
                } else {
                    readRes.put(GlobalVersion.VERSION_2008, res_2008_1 + res_2008_2);
                }
                currentVersion = GlobalVersion.VERSION_2008;
                session += "final res : " + readRes.values().toArray()[0] + "\n";
                return readRes;
            }
        }

        //2013
        session += "2013\n";
        sendAsyncNoRes("063033310D0A");
        String res_2013_1 = sendAsync("000001573102433128290307000001573102433128290307", 0, Common.TIME_OUT_1000, Common.TIME_OUT_500);
        progress += 1;
        progressCallback.update(progress);
        session += "res_2013_1 : " + res_2013_1 + "\n";
        while (res_2013_1.startsWith("15")) {
            res_2013_1 = res_2013_1.substring(2);
        }
        while (res_2013_1.endsWith("15")) {
            res_2013_1 = res_2013_1.substring(0, res_2013_1.length() - 2);
        }

        if (res_2013_1.isEmpty()) {
            progress += 2;
            progressCallback.update(progress);
            //maybe 2013
            String res_2013_2 = sendAsync("000001573102433328290307000001573102433328290307", 0, Common.TIME_OUT_1000, Common.TIME_OUT_500);
            progress += 2;
            progressCallback.update(progress);

            while (res_2013_2.startsWith("15")) {
                res_2013_2 = res_2013_2.substring(2);
            }

            while (res_2013_2.endsWith("15")) {
                res_2013_2 = res_2013_2.substring(0, res_2013_2.length() - 2);
            }
            session += "res_2013_2 : " + res_2013_2 + "\n";
            if (!res_2013_2.isEmpty()) {
                if ((res_2013_2.contains("024D5F534E28") || res_2013_2.contains("15024D5F534E28") || res_2013_2.contains("024D5F494428")) && res_2013_2.contains("0D0A210D0A0344")) {
                    readRes.put(GlobalVersion.VERSION_2013, res_2013_2);
                    session += "final res : " + readRes.values().toArray()[0] + "\n";
                    currentVersion = GlobalVersion.VERSION_2013;
                    return readRes;
                }
            }
        } else {
            progress += 2;
            progressCallback.update(progress);
            //maybe 2013 or 2016
            if ((res_2013_1.contains("024D5F534E28") || res_2013_1.contains("15024D5F534E28") || res_2013_1.contains("024D5F494428")) && res_2013_1.contains("0D0A210D0A0344")) {
                readRes.put(GlobalVersion.VERSION_2013, res_2013_1);
                currentVersion = GlobalVersion.VERSION_2013;
                session += "final res : " + readRes.values().toArray()[0] + "\n";
                return readRes;
            }

            while (res_2013_1.startsWith("15")) {
                res_2013_1 = res_2013_1.substring(2);
            }

            while (res_2013_1.endsWith("15")) {
                res_2013_1 = res_2013_1.substring(0, res_2013_1.length() - 2);
            }
            String res_2016_1 = sendAsync("000001573102433228290307", 1, Common.TIME_OUT_1000, Common.TIME_OUT_500);

            while (res_2016_1.startsWith("15")) {
                res_2016_1 = res_2016_1.substring(2);
            }

            while (res_2016_1.endsWith("15")) {
                res_2016_1 = res_2016_1.substring(0, res_2016_1.length() - 2);
            }
            if (res_2016_1.contains("4144455F56")) {
                sendAsyncNoRes("7F2F3F210D0A7F2F3F210D0A");
                sendAsyncNoRes("063033300D0A063033300D0A");
                res_2013_1 = sendAsync("000001573102433028290307", 1, Common.TIME_OUT_1000, Common.TIME_OUT_500);
                res_2016_1 = sendAsync("000001573102433128290307", 1, Common.TIME_OUT_1000, Common.TIME_OUT_500);
            }

            while (res_2013_1.startsWith("15")) {
                res_2013_1 = res_2013_1.substring(2);
            }

            while (res_2016_1.startsWith("15")) {
                res_2016_1 = res_2016_1.substring(2);
            }

            while (res_2013_1.endsWith("15")) {
                res_2013_1 = res_2013_1.substring(0, res_2013_1.length() - 2);
            }

            while (res_2016_1.endsWith("15")) {
                res_2016_1 = res_2016_1.substring(0, res_2016_1.length() - 2);
            }
            progress += 2;
            progressCallback.update(progress);
            session += "res_2016_1 : " + res_2016_1 + "\n";
            if ((res_2016_1.contains("024D5F534E28") || res_2016_1.contains("15024D5F534E28") || res_2016_1.contains("024D5F494428")) && res_2016_1.contains("0D0A210D0A0344")) {
                readRes.put(GlobalVersion.VERSION_2016, res_2016_1);
                currentVersion = GlobalVersion.VERSION_2016;
                session += "final res : " + readRes.values().toArray()[0] + "\n";
                return readRes;
            }
            if ((res_2013_1.contains("024D5F534E28") || res_2013_1.contains("15024D5F534E28") || res_2013_1.contains("024D5F494428")) && res_2013_1.contains("0D0A210D0A0344")) {
                readRes.put(GlobalVersion.VERSION_2016, res_2013_1);
                currentVersion = GlobalVersion.VERSION_2016;
                session += "final res : " + readRes.values().toArray()[0] + "\n";
                return readRes;
            }
            String res_2016_2 = sendAsync("000001573102433328290307000001573102433328290307", 1, Common.TIME_OUT_1000, Common.TIME_OUT_500);
            progress += 2;
            progressCallback.update(progress);
            session += "res_2016_2 : " + res_2016_2 + "\n";
            if ((res_2016_2.contains("024D5F534E28") || res_2016_2.contains("15024D5F534E28") || res_2016_2.contains("024D5F494428")) && res_2016_2.contains("0D0A210D0A0344")) {
                readRes.put(GlobalVersion.VERSION_2016, res_2016_2);
                currentVersion = GlobalVersion.VERSION_2016;
                session += "final res : " + readRes.values().toArray()[0] + "\n";
                return readRes;
            }
            readRes.put(GlobalVersion.VERSION_2016, res_2013_1 + res_2016_1);
            session += "final res : " + readRes.values().toArray()[0] + "\n";
            currentVersion = GlobalVersion.VERSION_2016;
            return readRes;
        }
        
        //2019
        session += "2019\n";
        if (isBluetooth) {
            baudRateSet = ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_N_9600_8_N_1);
        } 
        else{
            baudRateSet=ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_9600_8_N_1);
        }

        sendAsyncNoRes("2F3F210D0A");
        sendAsyncNoRes("063035310D0A");
        
        if (!isBluetooth) {
            ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_9600_8_N_1);
        }

        String res_2019_1 = sendAsync("7EA00800032193F05E7E7EA00800032193F05E7E", 1, Common.TIME_OUT_1000, Common.TIME_OUT_300);
        session += "res_2019_1 : " + res_2019_1 + "\n";

        progress += 1;
        progressCallback.update(progress);

        if (!res_2019_1.isEmpty()) {
            progress += 20;
            progressCallback.update(progress);
            String res_2019_2 = sendAsync("7EA02C00032110E2A5E6E600601DA109060760857405080101BE10040E01000000065F1F0400001E1DFFFFC5E47E", 2, Common.TIME_OUT_1000, Common.TIME_OUT_300);
            session += "res_2019_2 : " + res_2019_2 + "\n";

            progress += 20;
            progressCallback.update(progress);
            String res_2019_3 = sendAsync("7EA01A00032132BB48E6E600C001C1000100005E140AFF020041107E", 2, Common.TIME_OUT_1000, Common.TIME_OUT_1000);
            session += "res_2019_3 : " + res_2019_3 + "\n";
            progress += 20;
            progressCallback.update(progress);
            if ((res_2019_3.toUpperCase(Locale.ROOT).startsWith("7E") && res_2019_3.toUpperCase(Locale.ROOT).endsWith("7E"))) {
                readRes.put(GlobalVersion.VERSION_2019, res_2019_3);
                session += "final res : " + readRes.values().toArray()[0] + "\n";
                currentVersion = GlobalVersion.VERSION_2019;
                return readRes;
            } else {
                session += "final res : " + "null" + "\n";
                return null;
            }
        }
        if (isBluetooth) {
            baudRateSet = ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_R_9600_8_N_1);
        } 

        session += "final res : " + "null" + "\n";
        return null;
    }


    private static HashMap<GlobalVersion, String> readMeter(GlobalVersion globalVersion) {
        HashMap<GlobalVersion, String> readRes = new HashMap<>();

        boolean isBluetooth = ConnectionManager.Companion.getInstance().getConnectionType() == ConnectionType.BLUETOOTH;
        boolean baudRateSet;
        
        if (isBluetooth) {
            baudRateSet = ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_R_9600_8_N_1);
        } 
          

        switch (globalVersion) {
            case VERSION_2008:
                session += "2008\n";
                if (isBluetooth) {
                    baudRateSet = ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_N_2400_7_E_1);
                } 
                else{
                    baudRateSet=ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_2400_7_E_1);
                }
                String res_2008_1 = sendAsync("007F2F3F210D0A007F2F3F210D0A", 1, Common.TIME_OUT_1500, Common.TIME_OUT_500);
                session += "res_2008_1 : " + res_2008_1 + "\n";
                progress += 1;
                progressCallback.update(progress);
                if (res_2008_1.isEmpty()) {
                    res_2008_1 = sendAsync("7F2F3F210D0A7F2F3F210D0A", 1, Common.TIME_OUT_1500, Common.TIME_OUT_500);
                    session += "res_2008_1 : " + res_2008_1 + "\n";
                    progress += 1;
                    progressCallback.update(progress);
                }
                if (!res_2008_1.isEmpty()) {
                    //2008
                    progress += 10;
                    progressCallback.update(progress);
                    String res_2008_2 = sendAsync("063033300D0A063033300D0A", 1, Common.TIME_OUT_1500, Common.TIME_OUT_500);
                    session += "res_2008_2 : " + res_2008_2 + "\n";
                    if (res_2008_2.isEmpty()) {
                        if ((res_2008_1.contains("024D5F534E28") || res_2008_1.contains("15024D5F534E28") || res_2008_1.contains("024D5F494428")) && res_2008_1.contains("0D0A210D0A0344")) {
                            readRes.put(GlobalVersion.VERSION_2008, res_2008_1);
                            session += "final res : " + readRes.values().toArray()[0] + "\n";
                            currentVersion = GlobalVersion.VERSION_2008;
                            return readRes;
                        }
                    } else {
                        if ((res_2008_2.contains("024D5F534E28") || res_2008_2.contains("15024D5F534E28") || res_2008_2.contains("024D5F494428")) && res_2008_2.contains("0D0A210D0A0344")) {
                            readRes.put(GlobalVersion.VERSION_2008, res_2008_2);
                        } else {
                            readRes.put(GlobalVersion.VERSION_2008, res_2008_1 + res_2008_2);
                        }
                        currentVersion = GlobalVersion.VERSION_2008;
                        session += "final res : " + readRes.values().toArray()[0] + "\n";
                        return readRes;
                    }
                }
                break;
            case VERSION_2013:
                if (isBluetooth) {
                    baudRateSet = ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_N_2400_7_E_1);
                } 
                else{
                    baudRateSet=ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_2400_7_E_1);
                }
                //2013
                session += "2013\n";
                sendAsyncNoRes("063033310D0A");
                
                if (!isBluetooth) {
                    ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_2400_7_E_1);
                }

                String res_2013_1 = sendAsync("000001573102433128290307000001573102433128290307", 1, Common.TIME_OUT_1500, Common.TIME_OUT_500);
                progress += 1;
                progressCallback.update(progress);
                session += "res_2013_1 : " + res_2013_1 + "\n";
                while (res_2013_1.startsWith("15")) {
                    res_2013_1 = res_2013_1.substring(2);
                }
                while (res_2013_1.endsWith("15")) {
                    res_2013_1 = res_2013_1.substring(0, res_2013_1.length() - 2);
                }

                if (res_2013_1.isEmpty()) {
                    progress += 2;
                    progressCallback.update(progress);
                    //maybe 2013
                    String res_2013_2 = sendAsync("000001573102433328290307000001573102433328290307", 1, Common.TIME_OUT_1500, Common.TIME_OUT_500);
                    progress += 2;
                    progressCallback.update(progress);

                    while (res_2013_2.startsWith("15")) {
                        res_2013_2 = res_2013_2.substring(2);
                    }

                    while (res_2013_2.endsWith("15")) {
                        res_2013_2 = res_2013_2.substring(0, res_2013_2.length() - 2);
                    }
                    session += "res_2013_2 : " + res_2013_2 + "\n";
                    if (!res_2013_2.isEmpty()) {
                        if ((res_2013_2.contains("024D5F534E28") || res_2013_2.contains("15024D5F534E28") || res_2013_2.contains("024D5F494428")) && res_2013_2.contains("0D0A210D0A0344")) {
                            readRes.put(GlobalVersion.VERSION_2013, res_2013_2);
                            session += "final res : " + readRes.values().toArray()[0] + "\n";
                            currentVersion = GlobalVersion.VERSION_2013;
                            return readRes;
                        }
                    }
                } else {
                    progress += 2;
                    progressCallback.update(progress);
                    //maybe 2013 or 2016
                    if ((res_2013_1.contains("024D5F534E28") || res_2013_1.contains("15024D5F534E28") || res_2013_1.contains("024D5F494428")) && res_2013_1.contains("0D0A210D0A0344")) {
                        readRes.put(GlobalVersion.VERSION_2013, res_2013_1);
                        currentVersion = GlobalVersion.VERSION_2013;
                        session += "final res : " + readRes.values().toArray()[0] + "\n";
                        return readRes;
                    }

                    while (res_2013_1.startsWith("15")) {
                        res_2013_1 = res_2013_1.substring(2);
                    }

                    while (res_2013_1.endsWith("15")) {
                        res_2013_1 = res_2013_1.substring(0, res_2013_1.length() - 2);
                    }
                    String res_2016_1 = sendAsync("000001573102433228290307", 1, Common.TIME_OUT_1500, Common.TIME_OUT_500);

                    while (res_2016_1.startsWith("15")) {
                        res_2016_1 = res_2016_1.substring(2);
                    }

                    while (res_2016_1.endsWith("15")) {
                        res_2016_1 = res_2016_1.substring(0, res_2016_1.length() - 2);
                    }
                    if (res_2016_1.contains("4144455F56")) {
                        sendAsyncNoRes("7F2F3F210D0A7F2F3F210D0A");
                        sendAsyncNoRes("063033300D0A063033300D0A");
                        res_2013_1 = sendAsync("000001573102433028290307", 1, Common.TIME_OUT_1500, Common.TIME_OUT_500);
                        res_2016_1 = sendAsync("000001573102433128290307", 1, Common.TIME_OUT_1500, Common.TIME_OUT_500);
                    }

                    while (res_2013_1.startsWith("15")) {
                        res_2013_1 = res_2013_1.substring(2);
                    }

                    while (res_2016_1.startsWith("15")) {
                        res_2016_1 = res_2016_1.substring(2);
                    }

                    while (res_2013_1.endsWith("15")) {
                        res_2013_1 = res_2013_1.substring(0, res_2013_1.length() - 2);
                    }

                    while (res_2016_1.endsWith("15")) {
                        res_2016_1 = res_2016_1.substring(0, res_2016_1.length() - 2);
                    }
                    progress += 2;
                    progressCallback.update(progress);
                    session += "res_2016_1 : " + res_2016_1 + "\n";
                    if ((res_2016_1.contains("024D5F534E28") || res_2016_1.contains("15024D5F534E28") || res_2016_1.contains("024D5F494428")) && res_2016_1.contains("0D0A210D0A0344")) {
                        readRes.put(GlobalVersion.VERSION_2016, res_2016_1);
                        currentVersion = GlobalVersion.VERSION_2016;
                        session += "final res : " + readRes.values().toArray()[0] + "\n";
                        return readRes;
                    }
                    if ((res_2013_1.contains("024D5F534E28") || res_2013_1.contains("15024D5F534E28") || res_2013_1.contains("024D5F494428")) && res_2013_1.contains("0D0A210D0A0344")) {
                        readRes.put(GlobalVersion.VERSION_2016, res_2013_1);
                        currentVersion = GlobalVersion.VERSION_2016;
                        session += "final res : " + readRes.values().toArray()[0] + "\n";
                        return readRes;
                    }
                    String res_2016_2 = sendAsync("000001573102433328290307000001573102433328290307", 1, Common.TIME_OUT_1500, Common.TIME_OUT_500);
                    progress += 2;
                    progressCallback.update(progress);
                    session += "res_2016_2 : " + res_2016_2 + "\n";
                    if ((res_2016_2.contains("024D5F534E28") || res_2016_2.contains("15024D5F534E28") || res_2016_2.contains("024D5F494428")) && res_2016_2.contains("0D0A210D0A0344")) {
                        readRes.put(GlobalVersion.VERSION_2016, res_2016_2);
                        currentVersion = GlobalVersion.VERSION_2016;
                        session += "final res : " + readRes.values().toArray()[0] + "\n";
                        return readRes;
                    }
                    readRes.put(GlobalVersion.VERSION_2016, res_2013_1 + res_2016_1);
                    session += "final res : " + readRes.values().toArray()[0] + "\n";
                    currentVersion = GlobalVersion.VERSION_2016;
                    return readRes;
                }
                break;
            case VERSION_2019:
                //2019
                session += "2019\n";
                if (isBluetooth) {
                    baudRateSet = ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_N_9600_8_N_1);
                } 
                else{
                    if (!ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_9600_8_N_1)) {
                        return null;
                    }
                }
                sendAsyncNoRes("2F3F210D0A");

                sendAsyncNoRes("063035310D0A");

                if (isBluetooth) {
                    baudRateSet = ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_N_9600_8_N_1);
                } 
                else{
                    if (!ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_9600_8_N_1)) {
                        return null;
                    }
                }
                
                String res_2019_1 = sendAsync("7EA00800032193F05E7E7EA00800032193F05E7E", 1, Common.TIME_OUT_1500, Common.TIME_OUT_300);
                session += "res_2019_1 : " + res_2019_1 + "\n";

                progress += 1;
                progressCallback.update(progress);

                if (!res_2019_1.isEmpty()) {
                    progress += 20;
                    progressCallback.update(progress);
                    String res_2019_2 = sendAsync("7EA02C00032110E2A5E6E600601DA109060760857405080101BE10040E01000000065F1F0400001E1DFFFFC5E47E", 2, Common.TIME_OUT_1500, Common.TIME_OUT_300);
                    session += "res_2019_2 : " + res_2019_2 + "\n";

                    progress += 20;
                    progressCallback.update(progress);
                    String res_2019_3 = sendAsync("7EA01A00032132BB48E6E600C001C1000100005E140AFF020041107E", 2, Common.TIME_OUT_1500, Common.TIME_OUT_1000);
                    session += "res_2019_3 : " + res_2019_3 + "\n";
                    progress += 20;
                    progressCallback.update(progress);
                    if ((res_2019_3.toUpperCase(Locale.ROOT).startsWith("7E") && res_2019_3.toUpperCase(Locale.ROOT).endsWith("7E"))) {
                        readRes.put(GlobalVersion.VERSION_2019, res_2019_3);
                        session += "final res : " + readRes.values().toArray()[0] + "\n";
                        currentVersion = GlobalVersion.VERSION_2019;
                        return readRes;
                    } else {
                        session += "final res : " + "null" + "\n";
                        return null;
                    }
                }


                session += "final res : " + "null" + "\n";
                return null;
        }
        if (isBluetooth) {
            baudRateSet = ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_R_9600_8_N_1);
        } 

        return null;
    }


    private static int initVersionAsync(GlobalVersion globalVersion) {
        BaudRate baudRate,btBaudRate;
        boolean isBluetooth = ConnectionManager.Companion.getInstance().getConnectionType() == ConnectionType.BLUETOOTH;
        boolean baudRateSet;

        if (globalVersion == GlobalVersion.VERSION_2019) {
            baudRate = BaudRate.BAUD_9600_8_N_1;
            btBaudRate = BaudRate.BT_BAUD_N_9600_8_N_1;
        } else if (globalVersion == GlobalVersion.VERSION_2016) {
            baudRate = BaudRate.BAUD_2400_7_E_1;
            btBaudRate = BaudRate.BT_BAUD_N_2400_7_E_1;
        } else {
//            baudRate = BaudRate.BAUD_300_7_E_1;
            baudRate = BaudRate.BAUD_2400_7_E_1;
            btBaudRate = BaudRate.BT_BAUD_N_2400_7_E_1;

        }
//        boolean init = globalVersion == GlobalVersion.VERSION_2019 || globalVersion == GlobalVersion.VERSION_2016;
        boolean res ;
        if (isBluetooth) {
            res = ConnectionManager.Companion.getInstance().setBaudRateAsync(btBaudRate);
        } 
        else{        
            res = ConnectionManager.Companion.getInstance().setBaudRateAsync(baudRate);
        }
        if (res) {
            return 0;
        } else {
            if (ConnectionManager.Companion.getInstance().isConnected()) return -1;
            else return -2;
        }
    }
}