dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven { url uri('local-repo') } // 本地仓库，优先级最高
        maven { 
            url 'https://maven.aliyun.com/repository/google'
            allowInsecureProtocol = true
        }
        maven { 
            url 'https://maven.aliyun.com/repository/public'
            allowInsecureProtocol = true
        }
        maven { 
            url 'https://maven.aliyun.com/repository/gradle-plugin'
            allowInsecureProtocol = true
        }
        maven { 
            url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/'
            allowInsecureProtocol = true
        }
        maven { 
            url 'https://repo.huaweicloud.com/repository/maven/'
            allowInsecureProtocol = true
        }
        // google()
        // mavenCentral()
        // gradlePluginPortal()
        // maven { url = uri("https://plugins.gradle.org/m2/") }
        jcenter() // Warning: this repository is going to shut down soon
    }
}
rootProject.name = "Smart Meter Reader Tester"
include ':app'
include ':ConnectionManager'
include ':SmartMeterReader'
include ':ElectrometerParser'
include ':Common'
include ':ConnectionManager'
