# 使用镜像源下载 Android Gradle Plugin 8.3.2

$mirrors = @(
    "https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/8.3.2",
    "https://repo1.maven.org/maven2/com/android/tools/build/gradle/8.3.2"
)

$localDir = "local-repo\com\android\tools\build\gradle\8.3.2"

# 确保目录存在
if (!(Test-Path $localDir)) {
    New-Item -ItemType Directory -Path $localDir -Force
}

$files = @(
    "gradle-8.3.2.jar",
    "gradle-8.3.2.pom"
)

Write-Host "尝试从镜像源下载..." -ForegroundColor Green

foreach ($file in $files) {
    $downloaded = $false
    
    foreach ($mirror in $mirrors) {
        if ($downloaded) { break }
        
        $url = "$mirror/$file"
        $destination = Join-Path $localDir $file
        
        Write-Host "尝试从 $mirror 下载: $file" -ForegroundColor Yellow
        
        try {
            Invoke-WebRequest -Uri $url -OutFile $destination -UseBasicParsing -TimeoutSec 30
            Write-Host "✓ 成功下载: $file" -ForegroundColor Green
            $downloaded = $true
        }
        catch {
            Write-Host "✗ 从此镜像下载失败，尝试下一个..." -ForegroundColor Red
        }
    }
    
    if (-not $downloaded) {
        Write-Host "✗ 所有镜像都下载失败: $file" -ForegroundColor Red
        Write-Host "请手动下载文件并放置到: $localDir" -ForegroundColor Yellow
    }
}

Write-Host "`n如果下载成功，请运行: .\gradlew clean assembleDebug" -ForegroundColor Green 