# Android Gradle Plugin 8.3.2 下载脚本

$baseUrl = "https://dl.google.com/dl/android/maven2/com/android/tools/build/gradle/8.3.2"
$localDir = "local-repo\com\android\tools\build\gradle\8.3.2"

# 确保目录存在
if (!(Test-Path $localDir)) {
    New-Item -ItemType Directory -Path $localDir -Force
}

# 要下载的文件列表
$files = @(
    "gradle-8.3.2.jar",
    "gradle-8.3.2.pom"
)

Write-Host "开始下载 Android Gradle Plugin 8.3.2 文件..." -ForegroundColor Green

foreach ($file in $files) {
    $url = "$baseUrl/$file"
    $destination = Join-Path $localDir $file
    
    Write-Host "下载: $file" -ForegroundColor Yellow
    
    try {
        # 尝试使用 Invoke-WebRequest 下载
        Invoke-WebRequest -Uri $url -OutFile $destination -UseBasicParsing
        Write-Host "✓ 成功下载: $file" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ 下载失败: $file" -ForegroundColor Red
        Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "请手动下载: $url" -ForegroundColor Yellow
    }
}

Write-Host "`n下载完成！请运行 '.\gradlew clean assembleDebug' 进行构建" -ForegroundColor Green 