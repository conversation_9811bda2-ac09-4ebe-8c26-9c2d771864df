package shoaa.connectionmanager.bluetooth

import android.bluetooth.BluetoothDevice
import android.content.ComponentName
import android.content.Context
import android.content.ServiceConnection
import android.hardware.usb.UsbDevice
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import android.widget.Toast
import com.google.common.primitives.Bytes
import shoaa.common.Utils
import shoaa.connectionmanager.BaudRate
import shoaa.connectionmanager.Connected
import shoaa.connectionmanager.ConnectionInterface
import shoaa.connectionmanager.ConnectionManager
import shoaa.connectionmanager.TextUtil.fromHexString
import shoaa.connectionmanager.TextUtil.toHexString
import shoaa.connectionmanager.bluetooth.SerialService.SerialBinder
import java.io.IOException
import java.io.Serializable
import java.util.Locale
import java.util.concurrent.CompletableFuture

/**
 * Created by Islam Darwish
 */
internal class BluetoothHandler private constructor() : ServiceConnection, SerialListener,
    Serializable, ConnectionInterface {
    var session: String? = ""
    var service: SerialService? = null
    var connected: Connected? = null
    var selectedDeviceAddress: String? = null
    var bluetoothDeviceCo: BluetoothDeviceCo? = null
    var socket: SerialSocket? = null
    private var packet: MutableList<Byte>? = ArrayList()
    private var readTimeOut = 0
    private var expectedLength = 0
    private var sendTimeOut = 0
    private var initialStart = false
    private var retryCount = 0
    private var mTryCount = 0
    private var sendTime: Long = 0
    private var readTime: Long = 0
    private var device: BluetoothDevice? = null
    private var baudRate: BaudRate? = null

    override fun connectAsync(context: Context, device: UsbDevice, timeOut: Int): Int {
        throw Exception("Invalid Device")
    }

    override fun onServiceConnected(name: ComponentName, binder: IBinder) {
        instance!!.service = (binder as SerialBinder).service
        instance!!.service!!.attach(instance!!)
        if (instance!!.initialStart) {
            instance!!.initialStart = false
        }
    }

    override fun onServiceDisconnected(name: ComponentName) {
        instance!!.service = null
        instance!!.connected = Connected.False
    }

    private fun connect(context: Context, device: BluetoothDevice): Int {
        return try {
            this.device = device
            session = ""
            instance!!.connected = Connected.Pending
            instance!!.socket = SerialSocket(context, device)
            if (instance!!.service != null) {
                instance!!.service!!.connect(socket)
                instance!!.selectedDeviceAddress = device.address
                0
            } else {
                -1
            }
        } catch (e: Exception) {
            instance!!.connected = Connected.False
            instance!!.onSerialConnectError(e)
            instance!!.selectedDeviceAddress = ""
            Handler(Looper.getMainLooper()).post {
                // write your code here
                Toast.makeText(context, e.message, Toast.LENGTH_LONG).show()
            }
            -2
        }
    }

    internal fun writeSession(context: Context) {
        if (!session!!.isEmpty()) {
            Utils.writeStringAsFile(context, session, "Bluetooth.txt")
        }
        session = ""
    }

    override fun reset(): Boolean {
        return try {
            disconnect()
            Thread.sleep(1000)
            try {
                instance!!.connected = Connected.Pending
                if (instance!!.service != null) {
                    instance!!.service!!.connect(socket)
                    instance!!.selectedDeviceAddress = device!!.address
                    var time = 0
                    while (instance!!.connected == Connected.Pending) {
                        Thread.sleep(10)
                        time += 10
                        if (time >= 5000) {
                            break
                        }
                    }
                    instance!!.connected == Connected.True
                } else {
                    false
                }
            } catch (e: Exception) {
                instance!!.connected = Connected.False
                instance!!.onSerialConnectError(e)
                instance!!.selectedDeviceAddress = ""
                false
            }
        } catch (e: Exception) {
            false
        }
    }

    override fun connectAsync(context: Context, device: BluetoothDevice, timeOut: Int): Int {
        var ex = 0
        while (instance!!.retryCount < 2 && instance!!.connected != Connected.True) {
            ex = instance!!.connect(context, device)
            if (ex != 0) {
                return ex
            }
            var time = 0
            while (instance!!.connected == Connected.Pending) {
                try {
                    Thread.sleep(10)
                    time += 10
                    if (time >= timeOut / 2) {
                        ex = -3
                        break
                    }
                } catch (e: InterruptedException) {
                    time += 10
                    if (time >= timeOut / 2) {
                        ex = -4
                        break
                    }
                }
            }
            instance!!.retryCount++
        }
        if (instance!!.connected == Connected.True) {
            try {
                Thread.sleep(500)
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
//            bluetoothDeviceCo =
//                if (sendAsync("FAFA49443FFF", 1, 600, 300).replace(" ".toRegex(), "")
//                        .startsWith("49443D")
//                ) {
//                    BluetoothDeviceCo.ZPA
//                } else if (!sendAsync(
//                        toHexString("{\"CMD\":\"INFO?\"}".toByteArray(StandardCharsets.UTF_8)),
//                        1,
//                        600,
//                        300
//                    ).replace(" ".toRegex(), "").isEmpty()
//                ) {
//                    BluetoothDeviceCo.BLUESKY
//                } else {
//                    BluetoothDeviceCo.TESPRO
//                }
            bluetoothDeviceCo = BluetoothDeviceCo.TESPRO
        }
        instance!!.retryCount = 0
        return if (instance!!.connected == Connected.True) 0 else ex
    }

    override fun disconnect() {
        instance!!.connected = Connected.False
        instance!!.selectedDeviceAddress = ""
        if (instance!!.service != null) instance!!.service!!.disconnect()
    }

    override fun setBaudRateAsync(baudRate: BaudRate): Boolean {
//        if (this.baudRate == baudRate) {
//            return true
//        }
        if (instance?.connected != Connected.True) {
            return false
        }
        val res = BaudRateHandler.setBaudRateAsync(instance!!, baudRate, bluetoothDeviceCo)
        //        boolean res = new BaudRateHandler().setBaudRateAsync(instance, BaudRate, init);
        try {
            Thread.sleep(500)
        } catch (ignored: Exception) {
        }
        if (res) {
            this.baudRate = baudRate
        }
        return res
    }

    private fun send(hexString: String): String {
        val supply = CompletableFuture.supplyAsync {
            while (instance!!.mTryCount <= instance!!.retryCount) {
                try {
                    if (instance!!.service == null) {
                        session += "null service"
                        return@supplyAsync ""
                    }
                    session += "send: $hexString\n"
                    instance!!.service!!.write(fromHexString(hexString))
                    val wait = ConnectionManager.getInstance()
                        .calculateSendingTimeMs(baudRate?:BaudRate.BAUD_9600_8_N_1, fromHexString(hexString).size)
                    try {
                        Thread.sleep(wait.toLong())
                    } catch (ignore: Exception) {
                    }
                    instance!!.sendTime = System.currentTimeMillis()
                    while (System.currentTimeMillis() <= instance!!.sendTime + instance!!.sendTimeOut && instance!!.readTime == 0L) {
                        try {
                            Thread.sleep(50)
                        } catch (ignored: Exception) {
                        }
                    }
                    if (instance!!.readTime == 0L) {
                        instance!!.mTryCount++
                        continue
                    }
                    while (System.currentTimeMillis() <= instance!!.readTime + instance!!.readTimeOut) {
                        try {
                            Thread.sleep(50)
                        } catch (ignored: Exception) {
                        }
                    }
                    break
                } catch (e: IOException) {
                    instance!!.mTryCount++
                    try {
                        Thread.sleep(500)
                    } catch (ignored: Exception) {
                    }
                }
            }
            val res: String = toHexString(
                instance?.packet?.toByteArray() ?: byteArrayOf()
            ).replace(" ".toRegex(), "").uppercase(Locale.US)
            session += "res: $res\n\n"
            res
        }
        return try {
            supply.get()
        } catch (e: Exception) {
            session += e.message
            ""
        }
    }

    override fun sendAsync(
        hexString: String, retryCount: Int, sendTimeOut: Int, readTimeOut: Int
    ): String {
        if (instance!!.connected != Connected.True) {
            session += "not connected"
            return ""
        }
        instance!!.packet = ArrayList()
        instance!!.mTryCount = 0
        instance!!.readTime = 0
        instance!!.sendTime = 0
        instance!!.retryCount = retryCount
        instance!!.sendTimeOut = sendTimeOut
        instance!!.readTimeOut = readTimeOut
        return send(hexString.replace(" ".toRegex(), ""))
    }

    fun sendAsyncNoRes(hexString: String) {
        try {
            session += "sendAsyncNoRes: $hexString\n"
            instance!!.service!!.write(fromHexString(hexString))
            val wait = ConnectionManager.getInstance()
                .calculateSendingTimeMs(baudRate?:BaudRate.BAUD_9600_8_N_1, fromHexString(hexString).size)
            try {
                Thread.sleep(wait.toLong())
            } catch (ignore: Exception) {
            }
        } catch (_: Exception) {
        }
    }

    private fun sendWithLength(hexString: String): String {
        val supply = CompletableFuture.supplyAsync {
            while (instance!!.mTryCount <= instance!!.retryCount) {
                try {
                    if (instance!!.service == null) {
                        session += "null service"
                        return@supplyAsync ""
                    }
                   
                    instance!!.packet = ArrayList()
                    instance!!.service!!.write(fromHexString(hexString))
                    val wait = ConnectionManager.getInstance()
                        .calculateSendingTimeMs(baudRate?:BaudRate.BAUD_9600_8_N_1, fromHexString(hexString).size)
                    try {
                        Thread.sleep(wait.toLong())
                    } catch (ignore: Exception) {
                    }
                    instance!!.sendTime = System.currentTimeMillis()
                    
                    // 等待接收开始
                    var waitStartTime = System.currentTimeMillis()
                    while (System.currentTimeMillis() <= waitStartTime + instance!!.sendTimeOut && instance!!.readTime == 0L) {
                        try {
                            Thread.sleep(50)
                        } catch (ignored: Exception) {
                        }
                    }
                    
                    // 如果没有收到响应，重试
                    if (instance!!.readTime == 0L) {
                        instance!!.mTryCount++

                        continue
                    }
                    
                    // 等待完整响应
                    var waitResponseTime = System.currentTimeMillis()
                    while (System.currentTimeMillis() <= waitResponseTime + instance!!.readTimeOut && 
                           (instance!!.expectedLength == -1 || packet!!.size < instance!!.expectedLength)) {
                        if (instance!!.expectedLength == -1 && packet != null && packet!!.size > 1) {
                            instance!!.expectedLength =
                                (packet!![1].toInt() and 0xFF) + (packet!![2].toInt() and 0xFF) * 256
                            instance!!.expectedLength += 6
                        }
                        try {
                            Thread.sleep(50)
                        } catch (ignored: Exception) {
                        }
                    }
                    
                    // 检查是否收到完整响应
                    if (instance!!.expectedLength > 0 && packet!!.size < instance!!.expectedLength) {
                        instance!!.mTryCount++

                        continue
                    }
                    
                    break
                } catch (e: IOException) {
                    instance!!.mTryCount++
                    try {
                        Thread.sleep(500)
                    } catch (ignored: Exception) {
                    }                 
                }
            }
            
            val res: String = toHexString(
                instance?.packet?.toByteArray() ?: byteArrayOf()
            ).replace(" ".toRegex(), "").uppercase(Locale.US)
            session += "sendWithLength (" + instance!!.expectedLength + ") : " + hexString + "\n"
            session += "res (" + res.trim { it <= ' ' }.length / 2 + "): " + res + "\n\n"
            res
        }
        return try {
            supply.get()
        } catch (e: Exception) {
            session += e.message
            ""
        }
    }

    override fun sendWithLengthAsync(
        hexString: String, retryCount: Int, sendTimeOut: Int, expectedLength: Int
    ): String {
        if (instance!!.connected != Connected.True) {
            session += "not connected"
            return ""
        }
        instance!!.packet = ArrayList()
        instance!!.mTryCount = 0
        instance!!.readTime = 0
        instance!!.sendTime = 0
        instance!!.retryCount = retryCount
        instance!!.sendTimeOut = sendTimeOut
        instance!!.readTimeOut = if (expectedLength > 0) 800 else 2000
        instance!!.expectedLength = if (expectedLength > 0) expectedLength else -1
        var res = ""
        while ((res.length / 2 < instance!!.expectedLength - 1 || instance!!.expectedLength == -1) && instance!!.mTryCount <= instance!!.retryCount) {
            res = sendWithLength(hexString.replace(" ".toRegex(), ""))
            instance!!.mTryCount++
        }
        //        try {
//            return res.substring(0, expectedLength != 0 ? instance.expectedLength * 2 : ((instance.expectedLength * 2) + 2));
//        } catch (Exception e) {
//            return res;
//        }
        if (res.length / 2 != instance!!.expectedLength) {
            Log.e(
                "TAG",
                "unexpected Length : ex: " + instance!!.expectedLength + ", Res Len :" + res.length / 2
            )
        }
        return res
    }

    /*
     * SerialListener
     */
    override fun onSerialConnect() {
        instance!!.connected = Connected.True
    }

    override fun onSerialConnectError(e: Exception?) {
        instance!!.disconnect()
        instance!!.connected = Connected.False
    }

    override fun onSerialRead(data: ByteArray?) {
        if (data != null) {
            instance!!.packet!!.addAll(Bytes.asList(*data))
            instance!!.readTime = System.currentTimeMillis()
        }
    }

    override fun onSerialIoError(e: Exception?) {
        instance!!.disconnect()
        instance!!.connected = Connected.False
    }

    companion object {
        private var instance: BluetoothHandler? = null
        fun getInstance(): BluetoothHandler {
            if (instance == null) {
                instance = BluetoothHandler()
                instance!!.initialStart = true
                instance!!.connected = Connected.False
                instance!!.selectedDeviceAddress = ""
                instance!!.bluetoothDeviceCo = BluetoothDeviceCo.TESPRO
                instance!!.mTryCount = 0
                instance!!.readTimeOut = 0
                instance!!.sendTimeOut = 0
                instance!!.retryCount = 0
            }
            return instance!!
        }
    }
}
