graph TD
    subgraph Core
        A[ManagerEnums] --> A1[Add Constants]
        A1 --> A2[Update getBaud]
        A2 --> A3[Update stopBit]
        A3 --> A4[Update parity]
        
        B[BaudRateHandler] --> B1[Extend Async]
        B1 --> B2[Add TESPRO]
        B2 --> B3[Add ZPA]
        B3 --> B4[Update Data]
        
        C[BluetoothHandler] --> C1[Keep Interface]
        C1 --> C2[Call Handler]
    end
    
    subgraph BaudRates
        D[R Series] --> D1[R 9600]
        E[N Series] --> E1[N 9600]
        E1 --> E2[N 4800]
        E2 --> E3[N 2400]
        E3 --> E4[N 300]
        F[M Series] --> F1[M 9600]
        F1 --> F2[M 4800]
        F2 --> F3[M 2400]
        F3 --> F4[M 300]
    end
    
    subgraph Commands
        G[Before] --> G1[General]
        H[After] --> H1[N Standard]
        H1 --> H2[R Reset]
        H2 --> H3[M Manual]
    end
    
    subgraph Readers
        I[Global] --> I1{Version}
        I1 -->|2019| I2[9600]
        I1 -->|2016| I3[2400]
        I1 -->|Other| I4[2400]
        J[Iskra] --> J1[R 9600]
        K[Esmc] --> K1[R 9600]
        L[Maasara] --> L1[R 9600]
    end
    
    subgraph Validation
        M[Before] --> M1[OK]
        N[After] --> N1[Hex OK]
        N1 --> N2[Case]
    end
    
    subgraph Compat
        O[ZPA] --> O1[Empty]
        O1 --> O2[Compat]
        P[TESPRO] --> P1[Full]
        P1 --> P2[Prefix]
        Q[BLUESKY] --> Q1[JSON]
    end
    
    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D1 fill:#ffebee
    style E1 fill:#e8f5e8
    style E2 fill:#e8f5e8
    style E3 fill:#e8f5e8
    style E4 fill:#e8f5e8
    style F1 fill:#f3e5f5
    style F2 fill:#f3e5f5
    style F3 fill:#f3e5f5
    style F4 fill:#f3e5f5
    
    classDef coreFile fill:#e3f2fd,stroke:#1976d2
    classDef newConstant fill:#e8f5e8,stroke:#388e3c
    classDef command fill:#f3e5f5,stroke:#7b1fa2
    classDef improvement fill:#fff9c4,stroke:#f57c00
    
    class A,B,C coreFile
    class D1,E1,E2,E3,E4,F1,F2,F3,F4 newConstant
    class H1,H2,H3,N1,N2 improvement