flowchart TD
    subgraph "MeterReader.java - 核心调度器"
        A[MeterReader.read()] --> B{判断电表类型 MeterCo}
        B -->|GLOBAL| C[GlobalReader.read()]
        B -->|ISKRA| D[IskraReader.read()]
        B -->|ESMC| E[EsmcReader.read()]
        B -->|GPI| F[GpiReader.read()]
        B -->|MAASARA| G[MaasaraReader.read()]
        B -->|ELECTROMETER| H[ElectrometerReader.read()]
        B -->|Hay2a| I[Hay2aReader.read() - 新增]
        
        C --> C1[数据验证逻辑]
        D --> D1[数据验证逻辑]
        E --> E1[数据验证逻辑]
        F --> F1[数据验证逻辑]
        G --> G1[数据验证逻辑 - 大部分被注释]
        H --> H1[数据验证逻辑]
        I --> I1[无数据验证 - 新功能]
        
        C1 --> J[Thread.sleep(200-500ms)]
        D1 --> J
        E1 --> J
        F1 --> J
        G1 --> J
        H1 --> J
        I1 --> J
        
        J --> K[ConnectionManager.writeSession()]
        K --> L[返回ReadingResponse]
    end
    
    subgraph "MeterCo.java - 电表厂商枚举"
        M[电表厂商类型] --> M1[GLOBAL - 全球电表]
        M --> M2[ELECTROMETER - 电子电表]
        M --> M3[ESMC - ESMC电表]
        M --> M4[ISKRA - ISKRA电表]
        M --> M5[MAASARA - 马萨拉电表]
        M --> M6[GPI - GPI电表]
        M --> M7[Hay2a - 海亚电表 - 新增]
    end
    
    subgraph "Hay2aReader.java - 新增蓝牙电表读取器"
        N[Hay2aReader.read()] --> N1{检查连接状态}
        N1 -->|未连接| N2[尝试连接 connectAsync()]
        N1 -->|已连接| N3[检查蓝牙权限]
        N2 --> N3
        
        N3 --> N4[创建BluetoothManager]
        N4 --> N5[获取设备UUID]
        N5 --> N6[创建CommunicationPoint接口]
        
        N6 --> N7[实现OutputStream接口]
        N6 --> N8[实现InputStream接口]
        N6 --> N9[实现connect()方法]
        
        N9 --> N10[bluetoothManager.connectWithRetry()]
        N10 --> N11[创建ProgressCallback]
        N11 --> N12[调用KaiFaMeter.read()]
        N12 --> N13[解析JSON响应]
        N13 --> N14[设置SUCCESS状态]
        N14 --> N15[写入session文件]
    end
    
    subgraph "Hay2aResponse.java - 新增响应类"
        O[Hay2aResponse] --> O1[继承ReadingResponse]
        O1 --> O2[包含50+个电表数据字段]
        O2 --> O3[meter_ID, customer_ID, cardID]
        O2 --> O4[fw_version, activityType]
        O2 --> O5[电力因子, 安装信息]
        O2 --> O6[电表状态, 继电器状态]
        O2 --> O7[电池状态, 盖板状态]
        O2 --> O8[充值信息, 剩余额度]
        O2 --> O9[消费数据, 需量数据]
        O2 --> O10[12个月历史消费]
        
        O10 --> O11[toString()方法]
        O10 --> O12[toFileFormat()方法]
    end
    
    subgraph "BluetoothManager.java - 蓝牙连接管理"
        P[BluetoothManager] --> P1[connectWithRetry()方法]
        P1 --> P2{重试循环 attempt < retries}
        P2 -->|是| P3[获取蓝牙设备]
        P3 --> P4[检查蓝牙权限]
        P4 --> P5[创建RFCOMM Socket]
        P5 --> P6[取消设备发现]
        P6 --> P7[建立连接]
        P7 --> P8[获取输入输出流]
        P8 --> P9[连接成功]
        
        P2 -->|否| P10[连接失败]
        P7 -->|异常| P11[关闭连接]
        P11 --> P12[等待延迟时间]
        P12 --> P2
    end
    
    subgraph "数据验证逻辑对比"
        Q[修改前验证逻辑] --> Q1[所有电表都有严格验证]
        Q1 --> Q2[检查关键字段非零]
        Q1 --> Q3[验证电价等级1-7]
        Q1 --> Q4[检查月消费合理性]
        Q1 --> Q5[验证总消费 >= 月消费总和]
        
        R[修改后验证逻辑] --> R1[Hay2a电表无验证]
        R1 --> R2[MAASARA验证大部分注释]
        R1 --> R3[其他电表保持原验证]
        R1 --> R4[新增日期格式验证]
    end
    
    subgraph "ReadingResult.java - 结果状态枚举"
        S[读取结果状态] --> S1[SUCCESS - 成功]
        S --> S2[CAN_NOT_CONNECT - 无法连接]
        S --> S3[JSON_ERROR - JSON错误]
        S --> S4[CAN_NOT_GET_DATA - 无法获取数据]
        S --> S5[CAN_NOT_PARSE_DATA - 无法解析数据]
        S --> S6[CAN_NOT_SET_BaudRate - 无法设置波特率]
        S --> S7[OTHER - 其他错误]
    end
    
    style I fill:#e8f5e8
    style I1 fill:#e8f5e8
    style M7 fill:#e8f5e8
    style N fill:#e8f5e8
    style O fill:#e8f5e8
    style P fill:#e8f5e8
    style R1 fill:#fff3e0
    style R2 fill:#fff3e0
    
    classDef newFeature fill:#e8f5e8,stroke:#388e3c
    classDef modified fill:#fff3e0,stroke:#f57c00
    classDef core fill:#e3f2fd,stroke:#1976d2
    classDef validation fill:#f3e5f5,stroke:#7b1fa2
    
    class I,I1,M7,N,O,P newFeature
    class R1,R2,G1 modified
    class A,B,L,S core
    class Q1,Q2,Q3,Q4,Q5,R3,R4 validation