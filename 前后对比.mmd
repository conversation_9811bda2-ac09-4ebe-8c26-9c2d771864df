flowchart LR

    subgraph "Iskra电表读取流程"
        I1[开始] --> I2[发送HDLC退出命令\nsendIECExitCommandHDLC]
        I2 --> I3{连接类型判断}
        I3 -->|蓝牙| I4[发送重置波特率命令buadtrar]
        I4 --> I5[设置波特率baudtran,9600N81]
        I3 -->|USB| I6[设置波特率baudtran,300E71]
        I5 --> I7[再次发送HDLC退出命令]
        I6 --> I7
        I7 --> I8[正常握手流程]
        I8 --> I9{需要协商波特率?}
        I9 -->|是| I10{是蓝牙连接?<br>光电头内置自动波特率协商}
        I10 -->|否,USB连接| I11[设置波特率2400E71]
        I10 -->|是,蓝牙连接| I12[保持当前波特率]
        I9 -->|否| I12
        I11 --> I13[继续正常流程]
        I12 --> I13
    end

    subgraph "Maasara电表读取流程"
        M1[开始] --> M2[发送HDLC退出命令\nsendIECExitCommandHDLC]
        M2 --> M3{连接类型判断}
        M3 -->|蓝牙| M4[发送重置波特率命令buadtrar]
        M4 --> M5[设置波特率baudtran,9600N81]
        M3 -->|USB| M6[设置波特率300E71]
        M5 --> M7[再次发送HDLC退出命令]
        M6 --> M7
        M7 --> M8[正常握手流程]
        M8 --> M9{需要协商波特率?}
        M9 -->|是| M10{是蓝牙连接?<br>光电头内置自动波特率协商}
        M10 -->|否,USB连接| M11[设置波特率9600N81]
        M10 -->|是,蓝牙连接| M12[保持当前波特率]
        M9 -->|否| M12
        M11 --> M13[继续正常流程]
        M12 --> M13
    end

    subgraph "GPI电表读取流程"
        G1[开始] --> G2[发送HDLC退出命令\nsendIECExitCommandHDLC]
        G2 --> G3{连接类型判断}
        G3 -->|蓝牙| G4[发送重置波特率命令buadtrar]
        G4 --> G5[设置波特率baudtran,9600N81]
        G3 -->|USB| G6[设置波特率300E71]
        G5 --> G7[再次发送HDLC退出命令]
        G6 --> G7
        G7 --> G8[正常握手流程]
        G8 --> G9{需要协商波特率?}
        G9 -->|是| G10{是蓝牙连接?<br>光电头内置自动波特率协商}
        G10 -->|否,USB连接| G11[设置波特率9600N81]
        G10 -->|是,蓝牙连接| G12[保持当前波特率,删除了原来设置波特率命令]
        G9 -->|否| G12
        G11 --> G13[继续正常流程]
        G12 --> G13
    end

    subgraph "ESMC电表读取流程"
        E1[开始] --> E2[发送HDLC退出命令\nsendIECExitCommandHDLC]
        E2 --> E3{连接类型判断}
        E3 -->|蓝牙| E4[发送重置波特率命令buadtrar]
        E4 --> E5[设置波特率baudtran,9600N81]
        E3 -->|USB| E6[设置波特率9600N81]
        E5 --> E7[再次发送HDLC退出命令]
        E6 --> E7
        E7 --> E8[正常握手流程]
        E8 --> E9[继续正常流程]
    end

    subgraph "Global电表读取流程"
        GL1[开始] --> GL2[发送HDLC退出命令\nsendIECExitCommandHDLC]
        GL2 --> GL3{连接类型判断}
        GL3 -->|蓝牙| GL4[发送重置波特率命令buadtrar]
        GL4 --> GL5[设置波特率baudtran,2400E71]
        GL3 -->|USB| GL6[设置波特率2400E71]
        GL5 --> GL7[再次发送HDLC退出命令]
        GL6 --> GL7
        GL7 --> GL8[正常握手流程]
        GL8 --> GL9{需要协商波特率?<br>2008/2013/2019均是一样的处理方案}
        GL9 -->|是| GL10{是蓝牙连接?<br>光电头内置自动波特率协商}
        GL10 -->|否,USB连接| GL11[设置波特率2400E71]
        GL10 -->|是,蓝牙连接| GL12[保持当前波特率]
        GL9 -->|否| GL12
        GL11 --> GL13[继续正常流程]
        GL12 --> GL13
    end

    subgraph "Electrometerge表"
        D1[开始] --> D2[发送IEC退出命令]
        D2 --> D3{连接类型判断}
        D3 -->|蓝牙| D4[发送重置波特率命令buadtrar]
        D3 -->|USB| D5[设置波特率300E71]
        D4 --> D6[设置握手波特率baudtram,4800N81]
        D5 --> D7[开始握手过程]
        D6 --> D7
        D7 --> D8{需要协商波特率?}
        D8 -->|是| D9{是蓝牙连接?}
        D9 -->|是| D10[忽略波特率协商]
        D9 -->|否| D11[设置波特率4800N81]
        D8 -->|否| D10
        D10 --> D12[继续正常流程]
        D11 --> D12
    end
    
    style I1 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style I2 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style I3 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style I4 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style I5 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style I6 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style I7 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style I8 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style I9 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style I10 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style I11 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style I12 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style I13 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff

    style M1 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style M2 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style M3 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style M4 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style M5 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style M6 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style M7 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style M8 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style M9 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style M10 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style M11 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style M12 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style M13 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff

    style G1 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style G2 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style G3 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style G4 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style G5 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style G6 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style G7 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style G8 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style G9 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style G10 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style G11 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style G12 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style G13 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff

    style E1 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style E2 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style E3 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style E4 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style E5 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style E6 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style E7 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style E8 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style E9 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff

    style GL1 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style GL2 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style GL3 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style GL4 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style GL5 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style GL6 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style GL7 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style GL8 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style GL9 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style GL10 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style GL11 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style GL12 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style GL13 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff

    style D1 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style D2 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style D3 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style D4 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style D5 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style D6 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style D7 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style D8 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style D9 fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    style D10 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style D11 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    style D12 fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff

    classDef iskraFlow fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    classDef iskraDecision fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    
    classDef maasaraFlow fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    classDef maasaraDecision fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    
    classDef gpiFlow fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    classDef gpiDecision fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    
    classDef esmcFlow fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    classDef esmcDecision fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    
    classDef globalFlow fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    classDef globalDecision fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    
    classDef electrometerFlow fill:#2d2d2d,stroke:#4ecdc4,color:#ffffff
    classDef electrometerDecision fill:#2d2d2d,stroke:#ffd93d,color:#ffffff
    
    class I1,I2,I4,I5,I6,I7,I8,I11,I12,I13 iskraFlow
    class I3,I9,I10 iskraDecision
    class M1,M2,M4,M5,M6,M7,M8,M11,M12,M13 maasaraFlow
    class M3,M9,M10 maasaraDecision
    class G1,G2,G4,G5,G6,G7,G8,G11,G12,G13 gpiFlow
    class G3,G9,G10 gpiDecision
    class E1,E2,E4,E5,E6,E7,E8,E9 esmcFlow
    class E3 esmcDecision
    class GL1,GL2,GL4,GL5,GL6,GL7,GL8,GL11,GL12,GL13 globalFlow
    class GL3,GL9,GL10 globalDecision
    class D1,D2,D4,D5,D6,D7,D10,D11,D12 electrometerFlow
    class D3,D8,D9 electrometerDecision