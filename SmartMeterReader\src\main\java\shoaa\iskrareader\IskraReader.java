package shoaa.iskrareader;

import static shoaa.common.Utils.writeStringAsFile;

import android.content.Context;

import com.google.gson.Gson;

import java.util.Locale;

import shoaa.common.DateUtil;
import shoaa.common.ProgressCallback;
import shoaa.connectionmanager.BaudRate;
import shoaa.connectionmanager.Common;
import shoaa.connectionmanager.ConnectionManager;
import shoaa.connectionmanager.ConnectionType;
import shoaa.iskrareader.iskraemeco.optical.bluetooth.IskraemecoHandler;
import shoaa.iskrareader.iskraemeco.optical.bluetooth.model.ResponseModel;
import shoaa.smartmeterreader.ReadingResponse;
import shoaa.smartmeterreader.ReadingResult;

/**
 * Created by <PERSON> Darwish
 */

public class IskraReader {
    static ResponseModel responseInfo = new ResponseModel();
    static String pdu = "";
    static int sendErrorCount = 0;
    static String session = "";

    private static String sendAsync(String hexString, int retryCount, int sendTimeOut, int readTimeOut) {
        return ConnectionManager.Companion.getInstance().sendAsync(hexString, retryCount, sendTimeOut, readTimeOut);
    }

    public static ReadingResponse read(Context context, ProgressCallback progressCallback) {
        session = "";
        IskraemecoHandler iskraemecoHandler = new IskraemecoHandler();
        try {
            if (!ConnectionManager.Companion.getInstance().isConnected()) {
                if (ConnectionManager.Companion.getInstance().connectAsync(context, 5000) != 0) {
                    IskraResponse readingResponse = new IskraResponse();
                    readingResponse.readingResult = ReadingResult.CAN_NOT_CONNECT;
                    readingResponse.message = "";
                    return readingResponse;
                }
            }
            progressCallback.update(1);

            boolean isBluetooth = ConnectionManager.Companion.getInstance().getConnectionType() == ConnectionType.BLUETOOTH;
            boolean baudRateSet;
            
            progressCallback.update(2);
            if (!sendIECExitCommandHDLC()) {
//                Log.e("TAG", "Failed to send IEC HDLC exit command");
            }
            progressCallback.update(3);
            if (isBluetooth) {
                baudRateSet = ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_R_9600_8_N_1);
                baudRateSet = ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_N_2400_7_E_1);
            } else {
                baudRateSet = ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_300_7_E_1);
            }
            progressCallback.update(4);
            if (!sendIECExitCommandHDLC()) {
//                Log.e("TAG", "Failed to send IEC HDLCexit command");
            }            
            progressCallback.update(5);
            if (baudRateSet) {
                int tryCount = 0;
                String fileName = "";
                progressCallback.update(6);
                while (tryCount < 3 && (fileName.isEmpty()) || fileName.equalsIgnoreCase("null")) {
//                    Log.d("Iskra Reader", "tring to get file name attempt " + (tryCount + 1));
                    String handshake1 = iskraemecoHandler.handle("{\"service\":\"handshake\",\"pdu\":null,\"item\":null}");
                    responseInfo = new Gson().fromJson(handshake1, ResponseModel.class);
                    while (responseInfo.getResult().equalsIgnoreCase("continue")) {
                        String temp_pdu = sendAsync(responseInfo.getPdu(), 2, Common.TIME_OUT_1500, Common.TIME_OUT_500);
                        if (!temp_pdu.isEmpty()) pdu = temp_pdu;
                        String request = "{\"service\":\"handshake\",\"pdu\":\"" + pdu + "\",\"item\":null}";
                        String handshake2 = iskraemecoHandler.handle(request);
                        responseInfo = new Gson().fromJson(handshake2, ResponseModel.class);
                        if (responseInfo.getValue() != null && responseInfo.getValue().length() > 5) {
                            fileName = responseInfo.getValue();
                            break;
                        }
                    }
                    pdu = sendAsync(responseInfo.getPdu(), 2, Common.TIME_OUT_1500, Common.TIME_OUT_500);
                    String request2 = "{\"service\":\"acknowledge\",\"pdu\":\"" + pdu + "\",\"item\":null}";
                    String handshake3 = iskraemecoHandler.handle(request2);

                    responseInfo = new Gson().fromJson(handshake3, ResponseModel.class);
                    tryCount++;
                    if (tryCount < 3 && (fileName.isEmpty()) || fileName.equalsIgnoreCase("null")) {
                        try {
                            Thread.sleep(10000);
                        } catch (Exception ignore) {
                        }
                    }
                }

                IskraResponse readingResponse = new IskraResponse();
                if (responseInfo.getResult().equalsIgnoreCase("success")) {
                    if (fileName.isEmpty()) {
                        readingResponse = new IskraResponse();
                        readingResponse.readingResult = ReadingResult.OTHER;
                        readingResponse.message = "can not get file name";
                        return readingResponse;
                    }
                    progressCallback.update(7);
                    if (fileName.equalsIgnoreCase("ISKRA_1011_V1") || fileName.equalsIgnoreCase("ISKRA_1012_V1") || fileName.equalsIgnoreCase("ISKRA_1012_V3") || fileName.equalsIgnoreCase("ISKRA_1012_V4") || fileName.equalsIgnoreCase("ISKRA_1020_V5") || fileName.equalsIgnoreCase("ISKRA_3010") || fileName.equalsIgnoreCase("ISKRA_3012")) {
                        readingResponse.setMeterModel("0");
                        if (fileName.startsWith("ISKRA_3")) {
                            readingResponse.setMeterType("3Phase");
                        } else {
                            readingResponse.setMeterType("Single");
                        }
                        readingResponse.setFileName(fileName);
                        sendErrorCount = 0;
                        if (!isBluetooth) {
                            ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_2400_7_E_1);
                        }

                        //region ITEM_1_NEW_BASEITEM_Meter_ID
                        ResponseModel itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_1_NEW_BASEITEM_Meter_ID"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMeterId(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(8);
                        //region ITEM_2_NEW_BASEITEM_Customer_ID
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_2_NEW_BASEITEM_Customer_ID"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setCustomerId(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        //region ITEM_3_NEW_BASEITEM_CardID
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_3_NEW_BASEITEM_CardID"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setCardId(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(9);
                        //region ITEM_4_NEW_BASEITEM_fw_version
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_4_NEW_BASEITEM_fw_version"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setFwVersion(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        //region ITEM_5_NEW_BASEITEM_ActivityType
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_5_NEW_BASEITEM_ActivityType"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setActivityType(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(10);
                        //region ITEM_6_NEW_BASEITEM_curent_Power_factor
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_6_NEW_BASEITEM_curent_Power_factor"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setCurentPowerFactor(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(11);
                        //region ITEM_7_NEW_BASEITEM_last_year_Power_factor
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_7_NEW_BASEITEM_last_year_Power_factor"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setLastYearPowerFactor(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        //region ITEM_8_NEW_BASEITEM_installing_technican_code
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_8_NEW_BASEITEM_installing_technican_code"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setInstallingTechnicanCode(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(12);
                        //region ITEM_9_NEW_BASEITEM_installing_Date_and_time
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_9_NEW_BASEITEM_installing_Date_and_time"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
//                            221025113400,100.000,100.000,1
//                            datetime, charge amount, remain money, charge sequence
//                            22/10/25  11:34:00  , 100 LE , 100 LE , 1 charge
//                            Installing date and time  & charges at that time
                            try {
                                String res = String.valueOf(itemResponse.getValue()).split(",")[0];
                                if (res == null || res.length() != 12) {
                                    throw new Exception();
                                }
                                readingResponse.setInstallingDateAndTime(DateUtil.toApiFormat(DateUtil.parse("20" + res.substring(0, 2) + "/" + res.substring(2, 4) + "/" + res.substring(4, 6) + " " + res.substring(6, 8) + ":" + res.substring(8, 10) + ":" + res.substring(10))));
                            } catch (Exception e) {
                                readingResponse.setInstallingDateAndTime(String.valueOf(itemResponse.getValue()));
                            }
                        }
                        //endregion
                        //region ITEM_10_NEW_BASEITEM_Meter_Date_and_Time
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_10_NEW_BASEITEM_Meter_Date_and_Time"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMeterDateAndTime(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(13);
                        //region ITEM_12_NEW_BASEITEM_Current_tariff_activation_date
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_12_NEW_BASEITEM_Current_tariff_activation_date"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setCurrentTariffActivationDate(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(14);
                        //region ITEM_13_NEW_BASEITEM_Meter_status
                        // ITEM_15_NEW_BASEITEM_battery_status
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_13_NEW_BASEITEM_Meter_status"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String meterBattery = String.valueOf(itemResponse.getValue());
                            meterBattery = meterBattery.replaceAll(" ", "").toUpperCase(Locale.ROOT);
                            int batteryIndex = meterBattery.indexOf("B") + 1;
                            int meterIndex = meterBattery.indexOf("M") + 1;
                            if (meterIndex > 0 && ((meterIndex + 1) <= meterBattery.length()))
                                readingResponse.setMeterStatus(meterBattery.substring(meterIndex, meterIndex + 1));
                            if (batteryIndex > 0 && ((batteryIndex + 1) <= meterBattery.length()))
                                readingResponse.setBatteryStatus(meterBattery.substring(batteryIndex, batteryIndex + 1));
                        }
                        //endregion
                        progressCallback.update(15);
                        //region ITEM_14_NEW_BASEITEM_Relay_status
                        //ITEM_16_NEW_BASEITEM_Top_cover_status
                        //ITEM_17_NEW_BASEITEM_Side_cover_status
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_14_NEW_BASEITEM_Relay_status"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String state = String.valueOf(itemResponse.getValue());
                            state = state.replaceAll(" ", "").toUpperCase(Locale.ROOT);
                            int relayIndex = state.indexOf("R") + 1;
                            int sideCoverIndex = state.indexOf("S") + 1;
                            int topCoverIndex = state.indexOf("T") + 1;

                            int meterIndex = state.indexOf("M") + 1;
                            if (meterIndex > 0 && ((meterIndex + 1) <= state.length()) && readingResponse.getMeterStatus().equalsIgnoreCase("0"))
                                readingResponse.setMeterStatus(state.substring(meterIndex, meterIndex + 1));
                            if (relayIndex > 0 && ((relayIndex + 1) <= state.length()))
                                readingResponse.setRelayStatus(state.substring(relayIndex, relayIndex + 1));
                            if (topCoverIndex > 0 && ((topCoverIndex + 1) <= state.length()))
                                readingResponse.setTopCoverStatus(state.substring(topCoverIndex, topCoverIndex + 1));
                            if (sideCoverIndex > 0 && ((sideCoverIndex + 1) <= state.length()))
                                readingResponse.setSideCoverStatus(state.substring(sideCoverIndex, sideCoverIndex + 1));
                        }
                        //endregion

                        if (sendErrorCount > 10) {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                            return readingResponse;
                        }
                        sendErrorCount = 0;
                        //region ITEM_18_NEW_BASEITEM_Technical_code_event_1
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_18_NEW_BASEITEM_Technical_code_event_1"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setTechnicalCodeEvent1(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(16);
                        //region ITEM_19_NEW_BASEITEM_event_type_1
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_19_NEW_BASEITEM_event_type_1"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setEventType1(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(17);
                        //region ITEM_20_NEW_BASEITEM_event_Date_1
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_20_NEW_BASEITEM_event_Date_1"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setEventDate1(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(18);
                        //region ITEM_21_NEW_BASEITEM_Technical_code_event_2
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_21_NEW_BASEITEM_Technical_code_event_2"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setTechnicalCodeEvent2(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(19);
                        //region ITEM_22_NEW_BASEITEM_event_type_2
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_22_NEW_BASEITEM_event_type_2"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setEventType2(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(20);
                        //region ITEM_23_NEW_BASEITEM_event_Date_2
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_23_NEW_BASEITEM_event_Date_2"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setEventDate2(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(21);
                        //region ITEM_24_NEW_BASEITEM_Technical_code_event_3
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_24_NEW_BASEITEM_Technical_code_event_3"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setTechnicalCodeEvent3(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(22);
                        //region ITEM_25_NEW_BASEITEM_event_type_3
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_25_NEW_BASEITEM_event_type_3"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setEventType3(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(23);
                        //region ITEM_26_NEW_BASEITEM_event_Date_3
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_26_NEW_BASEITEM_event_Date_3"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setEventDate3(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(24);
                        //region ITEM_27_NEW_BASEITEM_recharge_number
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_27_NEW_BASEITEM_recharge_number"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setRechargeNumber(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        //region ITEM_28_NEW_BASEITEM_Recharge_Amount
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_28_NEW_BASEITEM_Recharge_Amount"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setRechargeAmount(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(25);
                        //region ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setLastRechargeDateAndTime(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(26);
                        //region ITEM_30_NEW_BASEITEM_remaining_credit_kw
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_30_NEW_BASEITEM_remaining_credit_kw"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setRemainingCreditKw(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(27);
                        //region ITEM_31_NEW_BASEITEM_remaining_credit_mony
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_31_NEW_BASEITEM_remaining_credit_mony"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setRemainingCreditMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(28);
                        //region ITEM_32_NEW_BASEITEM_Debts
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_32_NEW_BASEITEM_Debts"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setDebts(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        if (sendErrorCount > 10) {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                            return readingResponse;
                        }
                        sendErrorCount = 0;
                        progressCallback.update(29);
                        //region ITEM_33_NEW_BASEITEM_Total_consumption_kw
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_33_NEW_BASEITEM_Total_consumption_kw"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setTotalConsumptionKw(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(30);
                        //region ITEM_34_NEW_BASEITEM_Total_consumption_mony
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_34_NEW_BASEITEM_Total_consumption_mony"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setTotalConsumptionMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(31);
                        //region ITEM_35_NEW_BASEITEM_Total_consumption_kvar
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_35_NEW_BASEITEM_Total_consumption_kvar"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setTotalConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(32);
                        //region ITEM_36_NEW_BASEITEM_Current_Demand
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_36_NEW_BASEITEM_Current_Demand"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setCurrentDemand(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(33);
                        //region ITEM_37_NEW_BASEITEM_Maximum_Demand
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_37_NEW_BASEITEM_Maximum_Demand"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String maxDemond = String.valueOf(itemResponse.getValue());
                            readingResponse.setMaximumDemand(maxDemond.split(",")[0]);
                            readingResponse.setMaximumDemandDate(maxDemond.split(",")[1]);
                        }
                        //endregion
                        progressCallback.update(34);
                        //region ITEM_38_NEW_BASEITEM_Maximum_Demand_date
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_38_NEW_BASEITEM_Maximum_Demand_date"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximumDemandDate(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        //region ITEM_39_NEW_BASEITEM_instanteneous_pahase_A_volt
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_39_NEW_BASEITEM_instanteneous_pahase_A_volt"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setInstanteneousPahaseAVolt(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(35);
                        //region ITEM_40_NEW_BASEITEM_instanteneous_phase_B_volt
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_40_NEW_BASEITEM_instanteneous_phase_B_volt"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setInstanteneousPahaseBVolt(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(36);
                        //region ITEM_41_NEW_BASEITEM_instanteneous_phaase_C_volt
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_41_NEW_BASEITEM_instanteneous_phaase_C_volt"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setInstanteneousPahaseCVolt(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(37);
                        //region ITEM_42_NEW_BASEITEM_instanteneous_phase_A_current
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_42_NEW_BASEITEM_instanteneous_phase_A_current"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setInstanteneousPahaseACurrent(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(38);
                        //region ITEM_43_NEW_BASEITEM_instanteneous_phase_B_current
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_43_NEW_BASEITEM_instanteneous_phase_B_current"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setInstanteneousPahaseBCurrent(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(39);
                        //region ITEM_44_NEW_BASEITEM_instanteneous_phase_C_current
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_44_NEW_BASEITEM_instanteneous_phase_C_current"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setInstanteneousPahaseCCurrent(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(40);
                        //region ITEM_45_NEW_BASEITEM_instanteneous_volt
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_45_NEW_BASEITEM_instanteneous_volt"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setInstanteneousVolt(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(41);
                        //region ITEM_46_NEW_BASEITEM_instanteneous_current_Phase_Ampere
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_46_NEW_BASEITEM_instanteneous_current_Phase_Ampere"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setInstanteneousCurrentPhaseAmpere(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(42);
                        //region ITEM_47_NEW_BASEITEM_instanteneous_current_Neutral
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_47_NEW_BASEITEM_instanteneous_current_Neutral"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setInstanteneousCurrentNeutral(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(43);
                        //region ITEM_48_NEW_BASEITEM_reverse_Kwh
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_48_NEW_BASEITEM_reverse_Kwh"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setReverseKwh(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        if (sendErrorCount > 10) {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                            return readingResponse;
                        }
                        sendErrorCount = 0;
                        progressCallback.update(44);
                        //region ITEM_49_NEW_BASEITEM_unbalance_Kwh
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_49_NEW_BASEITEM_unbalance_Kwh"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setUnbalanceKwh(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        //region ITEM_50_NEW_BASEITEM_current_month_consumption_KW
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_50_NEW_BASEITEM_current_month_consumption_KW"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setCurrentMonthConsumptionKW(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion


                        //region ITEM_112_Tariff1
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_112_Tariff1"));
                        if (itemResponse == null || itemResponse.getValue() == null || ((!itemResponse.getValue().contains(",")) && (!itemResponse.getValue().replaceAll("[^0-9.]", "").matches("\\d+(\\.\\d+)?") || Double.parseDouble(itemResponse.getValue().replaceAll("[^0-9.]", "")) == 0.0)) || ((itemResponse.getValue().contains(",")) && (!itemResponse.getValue().split(",")[0].replaceAll("[^0-9.]", "").matches("\\d+(\\.\\d+)?") || Double.parseDouble(itemResponse.getValue().split(",")[0].replaceAll("[^0-9.]", "")) == 0.0))) {
//                            readingResponse.readingResult = ReadingResult.OTHER;
//                            readingResponse.message = "حدود شرائح غير معروفة";
//                            return readingResponse;
                            try {
                                int currentMonthConsumption = Integer.parseInt(readingResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "").split("\\.")[0]);
                                if (currentMonthConsumption >= 0 && currentMonthConsumption <= 50) {
                                    readingResponse.setCurrentTarrifInstalling("1");
                                } else if (currentMonthConsumption > 50 && currentMonthConsumption <= 100) {
                                    readingResponse.setCurrentTarrifInstalling("2");
                                } else if (currentMonthConsumption > 100 && currentMonthConsumption <= 200) {
                                    readingResponse.setCurrentTarrifInstalling("3");
                                } else if (currentMonthConsumption > 200 && currentMonthConsumption <= 350) {
                                    readingResponse.setCurrentTarrifInstalling("4");
                                } else if (currentMonthConsumption > 350 && currentMonthConsumption <= 650) {
                                    readingResponse.setCurrentTarrifInstalling("5");
                                } else if (currentMonthConsumption > 650 && currentMonthConsumption <= 1000) {
                                    readingResponse.setCurrentTarrifInstalling("6");
                                } else if (currentMonthConsumption > 1000) {
                                    readingResponse.setCurrentTarrifInstalling("7");
                                } else {
                                    readingResponse.setCurrentTarrifInstalling("1");
                                }
                            } catch (Exception e) {
                                readingResponse.readingResult = ReadingResult.OTHER;
                                readingResponse.message = "لم يتم التعرف علي الشريحة" + "\n" + "حدود شرائح غير معروفة" + "\n" + e.getMessage();
                                return readingResponse;
                            }
                        } else {
                            if (itemResponse.getValue().split(",").length > 1) {
                                String[] doubleValues = itemResponse.getValue().split(",");
                                for (int i = 0; i < doubleValues.length; i++) {
                                    if (i == 0) {
                                        readingResponse.setTariff1(doubleValues[i].replaceAll("[^0-9.-]", ""));
                                    } else if (i == 1) {
                                        readingResponse.setTariff2(doubleValues[i].replaceAll("[^0-9.-]", ""));
                                    } else if (i == 2) {
                                        readingResponse.setTariff3(doubleValues[i].replaceAll("[^0-9.-]", ""));
                                    } else if (i == 3) {
                                        readingResponse.setTariff4(doubleValues[i].replaceAll("[^0-9.-]", ""));
                                    } else if (i == 4) {
                                        readingResponse.setTariff5(doubleValues[i].replaceAll("[^0-9.-]", ""));
                                    } else if (i == 5) {
                                        readingResponse.setTariff6(doubleValues[i].replaceAll("[^0-9.-]", ""));
                                    } else if (i == 6) {
                                        readingResponse.setTariff7(doubleValues[i].replaceAll("[^0-9.-]", ""));
                                    } else if (i == 7) {
                                        readingResponse.setTariff8(doubleValues[i].replaceAll("[^0-9.-]", ""));
                                    } else if (i == 8) {
                                        readingResponse.setTariff9(doubleValues[i].replaceAll("[^0-9.-]", ""));
                                    } else if (i == 9) {
                                        readingResponse.setTariff10(doubleValues[i].replaceAll("[^0-9.-]", ""));
                                    }
                                }
                                String tariff = "0";
                                for (int i = 0; i < doubleValues.length; i++) {
                                    if (Double.parseDouble(readingResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) < Double.parseDouble(doubleValues[i].replaceAll("[^0-9.-]", ""))) {
                                        tariff = String.valueOf(i + 1);
                                        readingResponse.setCurrentTarrifInstalling(tariff);
                                        break;
                                    }
                                }
                                if (tariff.equals("0")) {
                                    tariff = String.valueOf(doubleValues.length + 1);
                                    readingResponse.setCurrentTarrifInstalling(tariff);
                                }
                            } else {
                                readingResponse.setCurrentTarrifInstalling("1");
                                if (itemResponse.getValue() != null) {
                                    readingResponse.setTariff1(itemResponse.getValue().replaceAll("[^0-9.]", ""));
                                    double maxTariff = Double.parseDouble(itemResponse.getValue().replaceAll("[^0-9.]", ""));
                                    if (maxTariff > 0 && Double.parseDouble(readingResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) > maxTariff) {
                                        readingResponse.setCurrentTarrifInstalling("2");
                                    }
                                }
                                //endregion
                                //region ITEM_112_Tariff2
                                itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_112_Tariff2"));
                                if (itemResponse != null && itemResponse.getValue() != null) {
                                    readingResponse.setTariff2(itemResponse.getValue().replaceAll("[^0-9.]", ""));
                                    double maxTariff = Double.parseDouble(itemResponse.getValue().replaceAll("[^0-9.]", ""));
                                    if (maxTariff > 0 && Double.parseDouble(readingResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) > maxTariff) {
                                        readingResponse.setCurrentTarrifInstalling("3");
                                    }
                                }
                                //endregion
                                //region ITEM_112_Tariff3

                                itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_112_Tariff3"));
                                if (itemResponse != null && itemResponse.getValue() != null) {
                                    readingResponse.setTariff3(itemResponse.getValue().replaceAll("[^0-9.]", ""));
                                    double maxTariff = Double.parseDouble(itemResponse.getValue().replaceAll("[^0-9.]", ""));
                                    if (maxTariff > 0 && Double.parseDouble(readingResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) > maxTariff) {
                                        readingResponse.setCurrentTarrifInstalling("4");
                                    }
                                }
                                //endregion
                                //region ITEM_112_Tariff4

                                itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_112_Tariff4"));
                                if (itemResponse != null && itemResponse.getValue() != null) {
                                    readingResponse.setTariff4(itemResponse.getValue().replaceAll("[^0-9.]", ""));
                                    double maxTariff = Double.parseDouble(itemResponse.getValue().replaceAll("[^0-9.]", ""));
                                    if (maxTariff > 0 && Double.parseDouble(readingResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) > maxTariff) {
                                        readingResponse.setCurrentTarrifInstalling("5");
                                    }
                                }
                                //endregion
                                //region ITEM_112_Tariff5

                                itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_112_Tariff5"));
                                if (itemResponse != null && itemResponse.getValue() != null) {
                                    readingResponse.setTariff5(itemResponse.getValue().replaceAll("[^0-9.]", ""));
                                    double maxTariff = Double.parseDouble(itemResponse.getValue().replaceAll("[^0-9.]", ""));
                                    if (maxTariff > 0 && Double.parseDouble(readingResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) > maxTariff) {
                                        readingResponse.setCurrentTarrifInstalling("6");
                                    }
                                }
                                //endregion
                                //region ITEM_112_Tariff6

                                itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_112_Tariff6"));
                                if (itemResponse != null && itemResponse.getValue() != null) {
                                    readingResponse.setTariff6(itemResponse.getValue().replaceAll("[^0-9.]", ""));
                                    double maxTariff = Double.parseDouble(itemResponse.getValue().replaceAll("[^0-9.]", ""));
                                    if (maxTariff > 0 && Double.parseDouble(readingResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) > maxTariff) {
                                        readingResponse.setCurrentTarrifInstalling("7");
                                    }
                                }
                                //endregion

//                                //region ITEM_112_Tariff7
//                                itemResponse = getItem( iskraemecoHandler,
//                                        IskraData.getCode(fileName, "ITEM_112_Tariff7"));
//                                if (itemResponse != null && itemResponse.getValue() != null) {
//                                    readingResponse.setTariff7(itemResponse.getValue().replaceAll("[^0-9.]", ""));
//                                    double maxTariff = Double.parseDouble(itemResponse.getValue().replaceAll("[^0-9.]", ""));
//                                    if (maxTariff > 0 && Double.parseDouble(readingResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) > maxTariff) {
//                                        readingResponse.setCurrentTarrifInstalling("8");
//                                    }
//                                }
//                                //endregion
//
//                                //region ITEM_112_Tariff8
//                                itemResponse = getItem( iskraemecoHandler,
//                                        IskraData.getCode(fileName, "ITEM_112_Tariff8"));
//                                if (itemResponse != null && itemResponse.getValue() != null) {
//                                    readingResponse.setTariff7(itemResponse.getValue().replaceAll("[^0-9.]", ""));
//                                    double maxTariff = Double.parseDouble(itemResponse.getValue().replaceAll("[^0-9.]", ""));
//                                    if (maxTariff > 0 && Double.parseDouble(readingResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) > maxTariff) {
//                                        readingResponse.setCurrentTarrifInstalling("9");
//                                    }
//                                }
//                                //endregion
//
//                                //region ITEM_112_Tariff9
//                                itemResponse = getItem( iskraemecoHandler,
//                                        IskraData.getCode(fileName, "ITEM_112_Tariff9"));
//                                if (itemResponse != null && itemResponse.getValue() != null) {
//                                    readingResponse.setTariff7(itemResponse.getValue().replaceAll("[^0-9.]", ""));
//                                    double maxTariff = Double.parseDouble(itemResponse.getValue().replaceAll("[^0-9.]", ""));
//                                    if (maxTariff > 0 && Double.parseDouble(readingResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) > maxTariff) {
//                                        readingResponse.setCurrentTarrifInstalling("10");
//                                    }
//                                }
//                                //endregion
//
//                                //region ITEM_112_Tariff10
//                                itemResponse = getItem( iskraemecoHandler,
//                                        IskraData.getCode(fileName, "ITEM_112_Tariff10"));
//                                if (itemResponse != null && itemResponse.getValue() != null) {
//                                    readingResponse.setTariff7(itemResponse.getValue().replaceAll("[^0-9.]", ""));
//                                    double maxTariff = Double.parseDouble(itemResponse.getValue().replaceAll("[^0-9.]", ""));
//                                    if (maxTariff > 0 && Double.parseDouble(readingResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) > maxTariff) {
//                                        readingResponse.setCurrentTarrifInstalling("11");
//                                    }
//                                }
//                                //endregion
                            }
                            //endregion
                            if (readingResponse.getCurrentTarrifInstalling().equalsIgnoreCase("0")) {
//                            readingResponse.readingResult = ReadingResult.OTHER;
//                            readingResponse.message = "لم يتم التعرف علي الشريحة";
//                            return readingResponse;
                                try {
                                    int currentMonthConsumption = Integer.parseInt(readingResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "").split("\\.")[0]);
                                    if (currentMonthConsumption >= 0 && currentMonthConsumption <= 50) {
                                        readingResponse.setCurrentTarrifInstalling("1");
                                    } else if (currentMonthConsumption > 50 && currentMonthConsumption <= 100) {
                                        readingResponse.setCurrentTarrifInstalling("2");
                                    } else if (currentMonthConsumption > 100 && currentMonthConsumption <= 200) {
                                        readingResponse.setCurrentTarrifInstalling("3");
                                    } else if (currentMonthConsumption > 200 && currentMonthConsumption <= 350) {
                                        readingResponse.setCurrentTarrifInstalling("4");
                                    } else if (currentMonthConsumption > 350 && currentMonthConsumption <= 650) {
                                        readingResponse.setCurrentTarrifInstalling("5");
                                    } else if (currentMonthConsumption > 650 && currentMonthConsumption <= 1000) {
                                        readingResponse.setCurrentTarrifInstalling("6");
                                    } else if (currentMonthConsumption > 1000) {
                                        readingResponse.setCurrentTarrifInstalling("7");
                                    } else {
                                        readingResponse.setCurrentTarrifInstalling("1");
                                    }
                                } catch (Exception e) {
                                    readingResponse.readingResult = ReadingResult.OTHER;
                                    readingResponse.message = "لم يتم التعرف علي الشريحة" + "\n" + e.getMessage();
                                    return readingResponse;
                                }
                            }
                        }
                        progressCallback.update(45);
                        //region ITEM_51_NEW_BASEITEM_current_month_consumption_MONY
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_51_NEW_BASEITEM_current_month_consumption_MONY"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setCurrentMonthConsumptionMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(46);
                        //region ITEM_52_NEW_BASEITEM_1_month_consumption_kWh
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_52_NEW_BASEITEM_1_month_consumption_kWh"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth1ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(47);
                        //region ITEM_53_NEW_BASEITEM_2_month_consumption_kWh
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_53_NEW_BASEITEM_2_month_consumption_kWh"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth2ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(48);
                        //region ITEM_54_NEW_BASEITEM_3_month_consumption_kWh
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_54_NEW_BASEITEM_3_month_consumption_kWh"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth3ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(49);
                        //region ITEM_55_NEW_BASEITEM_4_month_consumption_kWh
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_55_NEW_BASEITEM_4_month_consumption_kWh"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth4ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(50);
                        //region ITEM_56_NEW_BASEITEM_5_month_consumption_kWh
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_56_NEW_BASEITEM_5_month_consumption_kWh"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth5ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(51);
                        //region ITEM_57_NEW_BASEITEM_6_month_consumption_kWh
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_57_NEW_BASEITEM_6_month_consumption_kWh"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth6ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(52);
                        //region ITEM_58_NEW_BASEITEM_7_month_consumption_kWh
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_58_NEW_BASEITEM_7_month_consumption_kWh"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth7ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(53);
                        if (sendErrorCount > 10) {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                            return readingResponse;
                        }
                        sendErrorCount = 0;
                        //region ITEM_59_NEW_BASEITEM_8_month_consumption_kWh
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_59_NEW_BASEITEM_8_month_consumption_kWh"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth8ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(54);
                        //region ITEM_60_NEW_BASEITEM_9_month_consumption_kWh
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_60_NEW_BASEITEM_9_month_consumption_kWh"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth9ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        //region ITEM_61_NEW_BASEITEM_10_month_consumption_kWh
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_61_NEW_BASEITEM_10_month_consumption_kWh"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth10ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(55);
                        //region ITEM_62_NEW_BASEITEM_11_month_consumption_kWh
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_62_NEW_BASEITEM_11_month_consumption_kWh"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth11ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(56);
                        //region ITEM_63_NEW_BASEITEM_12_month_consumption_kWh
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_63_NEW_BASEITEM_12_month_consumption_kWh"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth12ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(57);
                        //region ITEM_64_NEW_BASEITEM_1_month_consumption_Mony
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_64_NEW_BASEITEM_1_month_consumption_Mony"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth1ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(58);
                        //region ITEM_65_NEW_BASEITEM_2_month_consumption_Mony
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_65_NEW_BASEITEM_2_month_consumption_Mony"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth2ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(59);
                        //region ITEM_66_NEW_BASEITEM_3_month_consumption_Mony
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_66_NEW_BASEITEM_3_month_consumption_Mony"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth3ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(60);
                        //region ITEM_67_NEW_BASEITEM_4_month_consumption_Mony
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_67_NEW_BASEITEM_4_month_consumption_Mony"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth4ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(61);
                        //region ITEM_68_NEW_BASEITEM_5_month_consumption_Mony
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_68_NEW_BASEITEM_5_month_consumption_Mony"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth5ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(62);
                        //region ITEM_69_NEW_BASEITEM_6_month_consumption_Mony
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_69_NEW_BASEITEM_6_month_consumption_Mony"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth6ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(63);
                        //region ITEM_70_NEW_BASEITEM_7_month_consumption_Mony
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_70_NEW_BASEITEM_7_month_consumption_Mony"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth7ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(64);
                        //region ITEM_71_NEW_BASEITEM_8_month_consumption_Mony
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_71_NEW_BASEITEM_8_month_consumption_Mony"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth8ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        //region ITEM_72_NEW_BASEITEM_9_month_consumption_Mony
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_72_NEW_BASEITEM_9_month_consumption_Mony"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth9ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(65);
                        //region ITEM_73_NEW_BASEITEM_10_month_consumption_Mony
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_73_NEW_BASEITEM_10_month_consumption_Mony"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth10ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(66);
                        //region ITEM_74_NEW_BASEITEM_11_month_consumption_Mony
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_74_NEW_BASEITEM_11_month_consumption_Mony"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth11ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        if (sendErrorCount > 10) {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                            return readingResponse;
                        }
                        sendErrorCount = 0;
                        progressCallback.update(67);
                        //region ITEM_75_NEW_BASEITEM_12_month_consumption_Mony
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_75_NEW_BASEITEM_12_month_consumption_Mony"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth12ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(69);
                        //region ITEM_76_NEW_BASEITEM_maxim_demand_month_1
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_76_NEW_BASEITEM_maxim_demand_month_1"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String value = String.valueOf(itemResponse.getValue());
                            if (value.split(",").length == 3) {
                                readingResponse.setMaximDemandMonth1(value.split(",")[1]);
                                readingResponse.setMaximDemandMonth1Date(value.split(",")[2]);
                            } else {
                                readingResponse.setMaximDemandMonth1("0.0");
                                readingResponse.setMaximDemandMonth1Date("1970-01-01 02:00:00");
                            }
                        }
                        //endregion
                        progressCallback.update(71);
                        //region ITEM_77_NEW_BASEITEM_maxim_demand_month_2
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_77_NEW_BASEITEM_maxim_demand_month_2"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String value = String.valueOf(itemResponse.getValue());
                            if (value.split(",").length == 3) {
                                readingResponse.setMaximDemandMonth2(value.split(",")[1]);
                                readingResponse.setMaximDemandMonth2Date(value.split(",")[2]);
                            } else {
                                readingResponse.setMaximDemandMonth2("0.0");
                                readingResponse.setMaximDemandMonth2Date("1970-01-01 02:00:00");
                            }
                        }
                        //endregion
                        progressCallback.update(73);
                        //region ITEM_78_NEW_BASEITEM_maxim_demand_month_3
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_78_NEW_BASEITEM_maxim_demand_month_3"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String value = String.valueOf(itemResponse.getValue());
                            if (value.split(",").length == 3) {
                                readingResponse.setMaximDemandMonth3(value.split(",")[1]);
                                readingResponse.setMaximDemandMonth3Date(value.split(",")[2]);
                            } else {
                                readingResponse.setMaximDemandMonth3("0.0");
                                readingResponse.setMaximDemandMonth3Date("1970-01-01 02:00:00");
                            }
                        }
                        //endregion
                        progressCallback.update(75);
                        //region ITEM_79_NEW_BASEITEM_maxim_demand_month_4
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_79_NEW_BASEITEM_maxim_demand_month_4"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String value = String.valueOf(itemResponse.getValue());
                            if (value.split(",").length == 3) {
                                readingResponse.setMaximDemandMonth4(value.split(",")[1]);
                                readingResponse.setMaximDemandMonth4Date(value.split(",")[2]);
                            } else {
                                readingResponse.setMaximDemandMonth4("0.0");
                                readingResponse.setMaximDemandMonth4Date("1970-01-01 02:00:00");
                            }
                        }
                        //endregion
                        progressCallback.update(77);
                        //region ITEM_80_NEW_BASEITEM_maxim_demand_month_5
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_80_NEW_BASEITEM_maxim_demand_month_5"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String value = String.valueOf(itemResponse.getValue());
                            if (value.split(",").length == 3) {
                                readingResponse.setMaximDemandMonth5(value.split(",")[1]);
                                readingResponse.setMaximDemandMonth5Date(value.split(",")[2]);
                            } else {
                                readingResponse.setMaximDemandMonth5("0.0");
                                readingResponse.setMaximDemandMonth5Date("1970-01-01 02:00:00");
                            }
                        }
                        //endregion
                        progressCallback.update(79);
                        //region ITEM_80_NEW_BASEITEM_maxim_demand_month_6
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_81_NEW_BASEITEM_maxim_demand_month_6"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String value = String.valueOf(itemResponse.getValue());
                            if (value.split(",").length == 3) {
                                readingResponse.setMaximDemandMonth6(value.split(",")[1]);
                                readingResponse.setMaximDemandMonth6Date(value.split(",")[2]);
                            } else {
                                readingResponse.setMaximDemandMonth6("0.0");
                                readingResponse.setMaximDemandMonth6Date("1970-01-01 02:00:00");
                            }
                        }
                        //endregion
                        progressCallback.update(81);
                        //region ITEM_82_NEW_BASEITEM_maxim_demand_month_7
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_82_NEW_BASEITEM_maxim_demand_month_7"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String value = String.valueOf(itemResponse.getValue());
                            if (value.split(",").length == 3) {
                                readingResponse.setMaximDemandMonth7(value.split(",")[1]);
                                readingResponse.setMaximDemandMonth7Date(value.split(",")[2]);
                            } else {
                                readingResponse.setMaximDemandMonth7("0.0");
                                readingResponse.setMaximDemandMonth7Date("1970-01-01 02:00:00");
                            }
                        }
                        //endregion
                        //region ITEM_83_NEW_BASEITEM_maxim_demand_month_8
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_83_NEW_BASEITEM_maxim_demand_month_8"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String value = String.valueOf(itemResponse.getValue());
                            if (value.split(",").length == 3) {
                                readingResponse.setMaximDemandMonth8(value.split(",")[1]);
                                readingResponse.setMaximDemandMonth8Date(value.split(",")[2]);
                            } else {
                                readingResponse.setMaximDemandMonth8("0.0");
                                readingResponse.setMaximDemandMonth8Date("1970-01-01 02:00:00");
                            }
                        }
                        //endregion
                        progressCallback.update(83);
                        //region ITEM_84_NEW_BASEITEM_maxim_demand_month_9
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_84_NEW_BASEITEM_maxim_demand_month_9"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String value = String.valueOf(itemResponse.getValue());
                            if (value.split(",").length == 3) {
                                readingResponse.setMaximDemandMonth9(value.split(",")[1]);
                                readingResponse.setMaximDemandMonth9Date(value.split(",")[2]);
                            } else {
                                readingResponse.setMaximDemandMonth9("0.0");
                                readingResponse.setMaximDemandMonth9Date("1970-01-01 02:00:00");
                            }
                        }
                        //endregion
                        progressCallback.update(85);
                        //region ITEM_85_NEW_BASEITEM_maxim_demand_month_10
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_85_NEW_BASEITEM_maxim_demand_month_10"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String value = String.valueOf(itemResponse.getValue());
                            if (value.split(",").length == 3) {
                                readingResponse.setMaximDemandMonth10(value.split(",")[1]);
                                readingResponse.setMaximDemandMonth10Date(value.split(",")[2]);
                            } else {
                                readingResponse.setMaximDemandMonth10("0.0");
                                readingResponse.setMaximDemandMonth10Date("1970-01-01 02:00:00");
                            }
                        }
                        //endregion
                        progressCallback.update(87);
                        //region ITEM_86_NEW_BASEITEM_maxim_demand_month_11
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_86_NEW_BASEITEM_maxim_demand_month_11"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String value = String.valueOf(itemResponse.getValue());
                            if (value.split(",").length == 3) {
                                readingResponse.setMaximDemandMonth11(value.split(",")[1]);
                                readingResponse.setMaximDemandMonth11Date(value.split(",")[2]);
                            } else {
                                readingResponse.setMaximDemandMonth11("0.0");
                                readingResponse.setMaximDemandMonth11Date("1970-01-01 02:00:00");
                            }
                        }
                        //endregion
                        progressCallback.update(89);
                        //region ITEM_87_NEW_BASEITEM_maxim_demand_month_12
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_87_NEW_BASEITEM_maxim_demand_month_12"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            String value = String.valueOf(itemResponse.getValue());
                            if (value.split(",").length == 3) {
                                readingResponse.setMaximDemandMonth12(value.split(",")[1]);
                                readingResponse.setMaximDemandMonth12Date(value.split(",")[2]);
                            } else {
                                readingResponse.setMaximDemandMonth12("0.0");
                                readingResponse.setMaximDemandMonth12Date("1970-01-01 02:00:00");
                            }
                        }
                        //endregion
                        progressCallback.update(90);
                        //region ITEM_100_NEW_BASEITEM_kvar_consumption_month_1
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_100_NEW_BASEITEM_kvar_consumption_month_1"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth1ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(91);
                        //region ITEM_101_NEW_BASEITEM_kvar_consumption_month_2
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_101_NEW_BASEITEM_kvar_consumption_month_2"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth2ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(92);
                        //region ITEM_102_NEW_BASEITEM_kvar_consumption_month_3
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_102_NEW_BASEITEM_kvar_consumption_month_3"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth3ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        if (sendErrorCount > 10) {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                            return readingResponse;
                        }
                        sendErrorCount = 0;
                        progressCallback.update(93);
                        //region ITEM_103_NEW_BASEITEM_kvar_consumption_month_4
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_103_NEW_BASEITEM_kvar_consumption_month_4"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth4ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(94);
                        //region ITEM_104_NEW_BASEITEM_kvar_consumption_month_5
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_104_NEW_BASEITEM_kvar_consumption_month_5"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth5ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        //region ITEM_105_NEW_BASEITEM_kvar_consumption_month_6
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_105_NEW_BASEITEM_kvar_consumption_month_6"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth6ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(95);
                        //region ITEM_106_NEW_BASEITEM_kvar_consumption_month_7
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_106_NEW_BASEITEM_kvar_consumption_month_7"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth7ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(96);
                        //region ITEM_107_NEW_BASEITEM_kvar_consumption_month_8
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_107_NEW_BASEITEM_kvar_consumption_month_8"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth8ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(97);
                        //region ITEM_108_NEW_BASEITEM_kvar_consumption_month_9
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_108_NEW_BASEITEM_kvar_consumption_month_9"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth9ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        //region ITEM_109_NEW_BASEITEM_kvar_consumption_month_10
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_109_NEW_BASEITEM_kvar_consumption_month_10"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth10ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(98);
                        //region ITEM_110_NEW_BASEITEM_kvar_consumption_month_11
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_110_NEW_BASEITEM_kvar_consumption_month_11"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth11ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(99);
                        //region ITEM_111_NEW_BASEITEM_kvar_consumption_month_12
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_111_NEW_BASEITEM_kvar_consumption_month_12"));
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth12ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion


                        //region check connection
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_1_NEW_BASEITEM_Meter_ID"));
                        if (itemResponse == null || itemResponse.getValue() == null || !(readingResponse.getMeterId().equalsIgnoreCase(String.valueOf(itemResponse.getValue())))) {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                            return readingResponse;
                        }
                        itemResponse = getItem(iskraemecoHandler, IskraData.getCode(fileName, "ITEM_2_NEW_BASEITEM_Customer_ID"));
                        if (itemResponse == null || itemResponse.getValue() == null || !(readingResponse.getCustomerId().equalsIgnoreCase(String.valueOf(itemResponse.getValue())))) {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                            return readingResponse;
                        }
                        //endregion

                    } else {
                        readingResponse = new IskraResponse();
                        readingResponse.readingResult = ReadingResult.OTHER;
                        readingResponse.message = "unknown meter";
                        return readingResponse;
                    }
                } else {
                    readingResponse = new IskraResponse();
                    readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                    readingResponse.message = "";
                    return readingResponse;
                }
                if (ConnectionManager.Companion.getInstance().isConnected()) {
                    progressCallback.update(100);
                    readingResponse.readingResult = ReadingResult.SUCCESS;
                } else {
                    readingResponse = new IskraResponse();
                    readingResponse.readingResult = ReadingResult.CAN_NOT_CONNECT;
                }
                readingResponse.message = "";
                writeStringAsFile(context, session, "session.txt");
                return readingResponse;
            } else {
                writeStringAsFile(context, session, "session.txt");
                ReadingResponse readingResponse = new IskraResponse();
                readingResponse.readingResult = ReadingResult.CAN_NOT_SET_BaudRate;
                readingResponse.message = "";
                return readingResponse;
            }
        } catch (Exception e) {
            writeStringAsFile(context, session, "session.txt");
            e.printStackTrace();
            IskraResponse readingResponse = new IskraResponse();
            readingResponse.readingResult = ReadingResult.OTHER;
            readingResponse.message = e.getMessage();
            return readingResponse;
        }
    }

    private static ResponseModel getItem(IskraemecoHandler iskraemecoHandler, String item) {
        if (item.equalsIgnoreCase("0")) return null;
        String request4 = "{\"service\":\"get\",\"pdu\":null,\"item\":\"" + item + "\"}";
        session += "OBIS : " + item + "\n";
        String response5 = iskraemecoHandler.handle(request4);
        try {

            ResponseModel responseModel = new Gson().fromJson(response5, ResponseModel.class);
            String pdu = sendAsync(responseModel.getPdu(), 1, Common.TIME_OUT_1200, Common.TIME_OUT_300);
            session += "sendApdu : " + responseModel.getPdu() + "\n";
            session += "res : " + pdu + "\n";
            if (pdu.isEmpty()) {
                sendErrorCount++;
                return null;
            }
            String request5 = "{\"service\":\"get\",\"pdu\":\"" + pdu + "\",\"item\":\"" + item + "\"}";
            String response6 = iskraemecoHandler.handle(request5);
            ResponseModel res = new Gson().fromJson(response6, ResponseModel.class);
            if (res == null || res.getValue() == null || res.getValue().isEmpty() || res.getValue().equalsIgnoreCase("NF"))
                return null;
            else if (res.getResult().equalsIgnoreCase("success")) {
                session += "value : " + res.getValue() + "\n\n";
                return res;
            } else {
                session += "value : error" + "\n\n";
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
        /**
     * Send IEC exit command with specific format (7E A0 0A 00 02 C0 01 03 53 CD 2A 7E)
     * @return whether command was sent successfully
     */
    private static boolean sendIECExitCommandHDLC() {
        String exitCommand = "0142300370";
        
        try {
            ConnectionManager.Companion.getInstance().sendAsync(exitCommand, 0, Common.TIME_OUT_300, Common.TIME_OUT_200);
            return true;
        } catch (Exception e) {
            //Log.e("TAG", "发送IEC退出命令时发生错误: " + e.getMessage());
            return false;
        }
    }
}
