plugins {
    id 'com.android.application'
}
android {
    signingConfigs {
//        config {
//            storeFile file('D:\\ShoaaProjects\\FinalApps\\Eslam\\SmartMeterReaderTester\\Shoaa_Key.jks')
//            storePassword '123456'
//            keyPassword '123456'
//            keyAlias 'Shoaa'
//        }
    }
    compileSdk 34
    buildToolsVersion "33.0.1"

    defaultConfig {
        applicationId "shoaa.smartmeterreadertester"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            //signingConfig signingConfigs.config
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            //signingConfig signingConfigs.config
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    namespace 'shoaa.smartmeterreadertester'
}

dependencies {
    implementation "androidx.multidex:multidex:2.0.1"
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'pub.devrel:easypermissions:3.0.0'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'org.jsoup:jsoup:1.17.2'

    implementation project(path: ':ConnectionManager')
    implementation project(path: ':Common')
    implementation project(path: ':SmartMeterReader')

}


tasks.register('buildJar') {
    buildJar.dependsOn ':ConnectionManager:createFullJarRelease'
    buildJar.dependsOn ':Common:createFullJarRelease'
    buildJar.dependsOn ':ElectrometerParser:createFullJarRelease'
    buildJar.dependsOn ':SmartMeterReader:createFullJarRelease'
    buildJar.doLast() {
        ant.unzip(src: '../ConnectionManager/build/intermediates/full_jar/release/full.jar', dest: 'build/tmp/jar_files')
        ant.unzip(src: '../Common/build/intermediates/full_jar/release/full.jar', dest: 'build/tmp/jar_files')
        ant.unzip(src: '../ElectrometerParser/build/intermediates/full_jar/release/full.jar', dest: 'build/tmp/jar_files')
        ant.unzip(src: '../SmartMeterReader/build/intermediates/full_jar/release/full.jar', dest: 'build/tmp/jar_files')
        ant.zip(destfile: 'build/jar/smart_meter_reader.jar') {
            fileset(dir: 'build/tmp/jar_files')
        }
        delete('build/tmp/jar_files')
    }
}