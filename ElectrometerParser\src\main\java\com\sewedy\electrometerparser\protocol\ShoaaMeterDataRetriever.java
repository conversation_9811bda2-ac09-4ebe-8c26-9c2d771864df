package com.sewedy.electrometerparser.protocol;

import static com.sewedy.electrometerparser.protocol.Shared.DataReplyWithDataPacket.CheckDataReplyWithDataPacket;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.BphRecords;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.ConfigureRecords;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.ControlRawData;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.EventRecords;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.MeteringRawData;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.MoneyRecords;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.ProfileRecords;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.TariffRawData;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.newControlRawData;

import android.os.Looper;
import android.util.Log;

import java.util.ArrayList;
import java.util.Timer;

import shoaa.common.ProgressCallback;
import shoaa.connectionmanager.BaudRate;
import shoaa.connectionmanager.Common;
import shoaa.connectionmanager.ConnectionManager;
import shoaa.connectionmanager.ConnectionType;
import shoaa.connectionmanager.TextUtil;


public class ShoaaMeterDataRetriever {
    private final ProgressCallback progressCallback;
    StringBuilder buffer = new StringBuilder();
    Timer timer1 = null;
    Timer timer2 = null;
    int readRecords = 0;
    Parser parser;
    MeterData meterData;
    Status upcomingStatus = Status.INIT_CONNECTION_DATA_STEP1;
    BaudRate currentBaudRate = BaudRate.BAUD_9600_8_N_1;
    GetSho3a3MeterDataCallback sho3a3MeterDataCallback;
    boolean isBeforeFinished = false;
    boolean isFirstTry = true;
    int bluetoothTryCount = 0;
    //    private boolean isFinishReadingMultipleRecords = false;
    private int numOfRecords = -1;

    public ShoaaMeterDataRetriever(ProgressCallback progressCallback) {
        parser = new Parser();
        MeterData.reset();
        MainMeterDataParser.reset();
        this.progressCallback = progressCallback;
    }

    public void dataHandler(String data) {
        int retryCount = 0;
        boolean success = false;
        try {
            if (isBeforeFinished) {
                return;
            }
            switch (this.upcomingStatus) {
                case METERING_DATA:
                    progressCallback.update(10);
                    writeMeteringCommand();
                    break;
                case PARSE_METERING:
                    progressCallback.update(20);
                    upcomingStatus = Status.TARRIF_DATA;
                    parseMeteringData(data);
                    break;
                case PARSE_TARRIF:
                    progressCallback.update(30);
                    upcomingStatus = Status.TAMPER_DATA;
                    parseTarriffData(data);
                    break;
                case PARSE_TAMPER:
                    progressCallback.update(40);
                    upcomingStatus = Status.BILLING_DATA;
                    parseTamperData(data);
                    break;
                case PARSE_BILLING:
                    progressCallback.update(50);
                    upcomingStatus = Status.MONEY_DATA;
                    parseBillingData(data);
                    break;
                case PARSE_MONEY:
                    progressCallback.update(60);
                    upcomingStatus = Status.CONTROL_DATA;
                    parseMoneyTransactions(data);
                    break;
                case PARSE_PROFILE:
                    progressCallback.update(70);
                    upcomingStatus = Status.METER_CONFIG_DATA;
                    parseProfiles(data);
                    break;
                case PARSE_METER_CONFIG:
                    progressCallback.update(80);
                    upcomingStatus = Status.PARSE_CONTROL;
                    parseConfigurations(data);
                    break;
                case PARSE_CONTROL:
                    progressCallback.update(90);
                    parseControlData(data);
                    //close socket and return output
//                            upcomingStatus = Status.EVENT_DATA;
//                            parseEvents(data);
                    break;

            }
        } catch (Exception e) {
            meterData.setMessage("Error:" + e.getMessage());
            returnWithError("Parse Error");
        }
    }

//    public void getAllData(GetSho3a3MeterDataCallback sho3a3MeterDataCallback) {
//        try {
//            Looper.prepare();
//            this.isBeforeFinished = false;
//            this.currentBaudRate = BaudRate.BaudTran_9600_8_N_1;
//            finalizeCurrentProcess();
//            this.sho3a3MeterDataCallback = sho3a3MeterDataCallback;
//            MeterData.reset();
//            this.meterData = MeterData.getInstance();
//            this.parser = new Parser();
//            MainMeterDataParser.reset();
//            Log.d("TAG", "Start**************");
//            intiBaudRate4800();
//            this.upcomingStatus = Status.INIT_CONNECTION_DATA_STEP1;
//            dataHandler("");
//        } catch (Exception e) {
////            meterData.setMessage("Error:" + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    private void intiBaudRate300() {
////        dataHandler(this.bluetoothHandler.sendAsync(ConnectionTypes.baudTran_300_7, 2, 1500, 300));
//        if (currentBaudRate != BaudRate.BaudTran_300_7_E_1) {
//            if (this.bluetoothHandler.setBaudRateAsync(BaudRate.BaudTran_300_7_E_1, false)) {
//                currentBaudRate = BaudRate.BaudTran_300_7_E_1;
//            }
//        }
//    }
//
//    private void intiBaudRate4800() {
//        if (currentBaudRate != BaudRate.BaudTran_4800_8_N_1) {
//            if (this.bluetoothHandler.setBaudRateAsync(BaudRate.BaudTran_4800_8_N_1, false)) {
//                currentBaudRate = BaudRate.BaudTran_4800_8_N_1;
//            }
//        }
//    }
//
//    private void initMeterConnectionStep1() {
//        byte[] requestPacket = {47, 63, 33, 13, 10};
//        upcomingStatus = Status.PARSE_CONNECTION_DATA_STEP1;
//        dataHandler(this.bluetoothHandler.sendWithLengthAsync(TextUtil.toHexString(requestPacket), isFirstTry ? 1 : 3, 2000, 16));
//    }
//
//    private void parseMeterConnectionStep1(String packet) {
//        byte[] data = Utils.hexStringToByteArray(packet);
//        String customerCode = "";
//        for (int i = 5; i < data.length - 2; i++) {
//            customerCode += (char) (data[i] & 0xFF);
//        }
//        meterData.setCustomerCode(customerCode);
////        Log.d("CustomerCode", customerCode);
//        upcomingStatus = Status.INIT_CONNECTION_DATA_STEP2;
//        dataHandler(packet);
//    }
//
//    private void initMeterConnectionStep2() {
//        byte[] arrAcknowledgement = {6, 48, 52, 54, 13, 10};
//        int expectedLength = 53;
//        String res = this.bluetoothHandler.sendWithLengthAsync(TextUtil.toHexString(arrAcknowledgement), isFirstTry ? 1 : 3, 2000, expectedLength);
//        if (res.isEmpty() || TextUtil.fromHexString(res).length < expectedLength) {
//            if (isFirstTry) {
//                if (!this.bluetoothHandler.reset()) {
//                    returnWithError("Connection Error");
//                }
//                intiBaudRate300();
//                upcomingStatus = Status.INIT_CONNECTION_DATA_STEP1;
//                isFirstTry = false;
//                dataHandler("");
//            } else {
//                Log.e("TAG_ERROR:", "initMeterConnectionStep2: ");
//                returnWithError("Connection Error");
//            }
//        } else {
//            intiBaudRate4800();
//            upcomingStatus = Status.PARSE_CONNECTION_DATA_STEP2;
//            dataHandler(res);
//        }
//    }
//
//    private void parseMeterConnectionStep2(String packet) {
//        MainMeterDataParser.getInstance().parseData(Utils.hexStringToByteArray(packet));
//        upcomingStatus = Status.METERING_DATA;
//        dataHandler(packet);
//    }

    public void getAllData(GetSho3a3MeterDataCallback sho3a3MeterDataCallback) {
        try {
            if (!ConnectionManager.Companion.getInstance().isConnected()) {
                return;
            }
            Looper.prepare();
            this.isBeforeFinished = false;
            this.currentBaudRate = BaudRate.BAUD_9600_8_N_1;
            finalizeCurrentProcess();
            this.sho3a3MeterDataCallback = sho3a3MeterDataCallback;
            MeterData.reset();
            this.meterData = MeterData.getInstance();
            this.parser = new Parser();
            MainMeterDataParser.reset();
            Log.d("TAG", "Start**************");
            this.bluetoothTryCount = 0;
            initConnection();
        } catch (Exception e) {
//            meterData.setMessage("Error:" + e.getMessage());
            e.printStackTrace();
        }
    }

    private boolean initBaudRate300() {
//        dataHandler(ConnectionManager.Companion.sendAsync(ConnectionTypes.baudTran_300_7, 2, 1500, 300));
        if (currentBaudRate != BaudRate.BAUD_300_7_E_1) {
            if (ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_300_7_E_1)) {
                currentBaudRate = BaudRate.BAUD_300_7_E_1;
                return true;
            } else {
                returnWithError("Can not Set BaudRate 300");
                return false;
            }
        }
        return true;
    }

    private boolean initBaudRate4800() {
        if (currentBaudRate != BaudRate.BT_BAUD_N_4800_8_N_1) {
            if (ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_N_4800_8_N_1)) {
                currentBaudRate = BaudRate.BT_BAUD_N_4800_8_N_1;
                return true;
            } else {
                returnWithError("Can not Set BaudRate 4800");
                return false;
            }
        }
        return true;
    }

    void initConnection() {
        if (ConnectionManager.Companion.getInstance().getConnectionType() == ConnectionType.BLUETOOTH) {
            initBluetooth();
        } else {
            initUSB();
        }
    }

    void initBluetooth() {
        progressCallback.update(6);
        
        // Send IEC exit command
        if (!sendIECExitCommand()) {
            returnWithError("Failed to send IEC exit command");
            return;
        }
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Log.e("TAG", "Interrupted while waiting for IEC exit command completion: " + e.getMessage());
        }
        
        // Reset Bluetooth optical head parameters
        if (!resetBluetoothOpticalHead()) {
            returnWithError("Failed to reset Bluetooth optical head parameters");
            return;
        }
        progressCallback.update(7);

        // Set communication baud rate
        if (!setCommunicationBaudRate()) {
            returnWithError("Failed to set communication baud rate");
            return;
        }
        progressCallback.update(8);

        // Send IEC exit command
        if (!sendIECExitCommand()) {
            returnWithError("Failed to send IEC exit command");
            return;
        }

        progressCallback.update(9);

        // Set intermediate baud rate
        if (!setIntermediateBaudRate()) {
            returnWithError("Failed to set intermediate baud rate");
            return;
        }
        progressCallback.update(10);

        if (!initBluetoothStep1()) {
            return;
        }
        progressCallback.update(11);
        if (!initBluetoothStep2()) {
            return;
        }
        progressCallback.update(12);
        upcomingStatus = Status.METERING_DATA;
        dataHandler("");
    }

    boolean switchBaudRate() {
        if (currentBaudRate == BaudRate.BAUD_300_7_E_1) {
            return initBaudRate4800();
        } else {
            return initBaudRate300();
        }
    }

    boolean initBluetoothStep1() {
        byte[] requestPacket = {47, 63, 33, 13, 10};
        int expectedLength = 16;
        String res = ConnectionManager.Companion.getInstance().sendWithLengthAsync(TextUtil.INSTANCE.toHexString(requestPacket), 2, Common.TIME_OUT_2000, expectedLength);
        if (res.length() / 2 < expectedLength) {
            if (bluetoothTryCount < 5 && !isBeforeFinished) {
                bluetoothTryCount++;


                if (switchBaudRate()) {
                    return initBluetoothStep1();
                } else {
                    return false;
                }
            } else {
                Log.e("TAG_ERROR:", "initMeterConnectionStep1: ");
                returnWithError("Connection Error");
                return false;
            }
        } else {
            byte[] data = Utils.hexStringToByteArray(res);
            String customerCode = "";
            for (int i = 5; i < data.length - 2; i++) {
                customerCode += (char) (data[i] & 0xFF);
            }
            meterData.setCustomerCode(customerCode);
            return true;
        }
    }

    boolean initBluetoothStep2() {
        byte[] arrAcknowledgement = {6, 48, 52, 54, 13, 10};
        int expectedLength = 53;
        String res = ConnectionManager.Companion.getInstance().sendWithLengthAsync(TextUtil.INSTANCE.toHexString(arrAcknowledgement),
                (currentBaudRate == BaudRate.BAUD_300_7_E_1) ? 0 : 1,
                Common.TIME_OUT_2000,
                expectedLength);
        if ((res.length() / 2) < expectedLength) {
            if (bluetoothTryCount < 5 && !isBeforeFinished) {
                bluetoothTryCount++;
                if (switchBaudRate()) {
                    return initBluetoothStep1();
                } else {
                    return false;
                }
            } else {
                Log.e("TAG_ERROR:", "initMeterConnectionStep2: ");
                returnWithError("Connection Error");
                return false;
            }
        } else {
            MainMeterDataParser.getInstance().parseData(Utils.hexStringToByteArray(res));
            return true;
        }
    }

    void initUSB() {
        progressCallback.update(6);
        ConnectionManager.Companion.getInstance().sendAsync("7F2F3F210D0A", 0, Common.TIME_OUT_300, Common.TIME_OUT_200);
        ConnectionManager.Companion.getInstance().sendAsync("063030360D0A", 0, Common.TIME_OUT_300, Common.TIME_OUT_200);
        if (!initBaudRate300()) {
            return;
        }
        progressCallback.update(7);
        if (!initUsbStep1()) {
            return;
        }
        progressCallback.update(8);

        if (!initUsbStep2()) {
            return;
        }
        progressCallback.update(9);
        upcomingStatus = Status.METERING_DATA;
        dataHandler("");
    }

    private boolean initUsbStep1() {
        byte[] requestPacket = {47, 63, 33, 13, 10};
        int expectedLength = 16;
        String res = ConnectionManager.Companion.getInstance().sendWithLengthAsync(TextUtil.INSTANCE.toHexString(requestPacket), 3, Common.TIME_OUT_2000, expectedLength);
        if (res.length() < expectedLength) {
            if (currentBaudRate == BaudRate.BAUD_300_7_E_1) {
                if (!initBaudRate4800()) {
                    return false;
                }
                return initUsbStep1();
            } else {
                Log.e("TAG_ERROR:", "initMeterConnectionStep1: ");
                returnWithError("Connection Error");
                return false;
            }
        } else {
            byte[] data = Utils.hexStringToByteArray(res);
            String customerCode = "";
            for (int i = 5; i < data.length - 2; i++) {
                customerCode += (char) (data[i] & 0xFF);
            }
            meterData.setCustomerCode(customerCode);
            return true;
        }
    }

    private boolean initUsbStep2() {
        byte[] arrAcknowledgement = {6, 48, 52, 54, 13, 10};
        int expectedLength = 53;
        String res = ConnectionManager.Companion.getInstance()
                .sendAndSwitchBaudRateWithLengthAsync(TextUtil.INSTANCE.toHexString(arrAcknowledgement),
                        0,
                        2000,
                        expectedLength,
                        currentBaudRate,
                        BaudRate.BAUD_4800_8_N_1);

        if (res.isEmpty() || (res.length() / 2) < expectedLength) {
            Log.e("TAG_ERROR:", "initMeterConnectionStep2: ");
            returnWithError("Connection Error");
            return false;
        } else {
            MainMeterDataParser.getInstance().parseData(Utils.hexStringToByteArray(res));
            return true;
        }
    }
/////

    private void writeMeteringCommand() {
        Log.d("TAG", "writeMeteringCommand");
        Shared.DataRequestDataPacket RequestDataPacketPkt = new Shared.DataRequestDataPacket(MeteringRawData, Shared.DataNewDataListPacket.endianType);
        byte[] arrRequestData = RequestDataPacketPkt.ComposePacket();
        upcomingStatus = Status.PARSE_METERING;
        dataHandler(ConnectionManager.Companion.getInstance().sendWithLengthAsync(TextUtil.INSTANCE.toHexString(arrRequestData), 5, Common.TIME_OUT_500, 0));
    }

    private void parseMeteringData(String packet) {
        Log.d("TAG", "parseMeteringData");
        try {
            if (CheckDataReplyWithDataPacket(Utils.hexStringToByteArray(packet), MeteringRawData)) {
                Shared.MeteringData meteringData = parser.splitMeteringData(MainMeterDataParser.getInstance().getData(), Utils.hexStringToByteArray(packet));
                meterData.setMeteringData(meteringData);
            } else {
                returnWithError("Parse MeteringData: fail");
                return;
            }
            upcomingStatus = Status.PARSE_TARRIF;
            writeTarriffData();
        } catch (Exception e) {
            e.printStackTrace();
            returnWithError(e.getMessage());
        }
    }

    private void writeTarriffData() {
        Log.d("TAG", "writeTarriffData: ");
        Shared.DataRequestDataPacket RequestDataPacketPkt = new Shared.DataRequestDataPacket(TariffRawData, Shared.DataNewDataListPacket.endianType);
        byte[] arrRequestData = RequestDataPacketPkt.ComposePacket();
        dataHandler(ConnectionManager.Companion.getInstance().sendWithLengthAsync(TextUtil.INSTANCE.toHexString(arrRequestData), 5, Common.TIME_OUT_500, 0));
    }

    private void parseTarriffData(String packet) {
        try {
            Log.d("TAG", "parseTarriffData");
            if (CheckDataReplyWithDataPacket(Utils.hexStringToByteArray(packet), TariffRawData)) {
                Log.d("TAG", "Parse TarriffData: success");
                Shared.TarrifPaymentData tarrifPaymentData = parser.SplitTarifAndPaymentData(Utils.hexStringToByteArray(packet), MainMeterDataParser.getInstance().getData());
                meterData.setTarrifPaymentData(tarrifPaymentData);
            } else {
                returnWithError("Parse TarriffData: failed");
                return;
            }
            upcomingStatus = Status.PARSE_TAMPER;
            writeTamperData();
        } catch (Exception e) {
            e.printStackTrace();
            returnWithError(e.getMessage());
        }
    }

    private void writeTamperData() {
        Log.d("TAG", "writeTamperData: ");
        Shared.DataRequestDataPacket RequestDataPacketPkt = new Shared.DataRequestDataPacket(ControlRawData, Shared.DataNewDataListPacket.endianType);
        byte[] arrRequestData = RequestDataPacketPkt.ComposePacket();
        dataHandler(ConnectionManager.Companion.getInstance().sendWithLengthAsync(TextUtil.INSTANCE.toHexString(arrRequestData),5, Common.TIME_OUT_500, 0));
    }

    private void parseTamperData(String packet) {
        try {
            Shared.ControlTemperDT1 controlTemperDT1 = null;
            if (CheckDataReplyWithDataPacket(Utils.hexStringToByteArray(packet), ControlRawData)) {
                Log.d("TAG", "Parse ControlTemperData: success");
                controlTemperDT1 = parser.SplitControlAndTamperData(MainMeterDataParser.getInstance().getData(), Utils.hexStringToByteArray(packet));
                if (controlTemperDT1 == null) {
                    returnWithError("Parse ControlTemperData: failed");
                    return;
                } else {
                    meterData.setControlTemperDT1(controlTemperDT1);
                }
            } else {
                returnWithError("Parse ControlTemperData: failed");
                return;
            }
            meterData.setControlTemperDT1(controlTemperDT1);
            upcomingStatus = Status.PARSE_BILLING;
            writeBillingHistory();
        } catch (Exception e) {
            e.printStackTrace();
            returnWithError(e.getMessage());
        }
    }

    private void writeBillingHistory() {
        readRecords = 0;
        numOfRecords = -1;
        Log.d("BillingRecords:", MainMeterDataParser.getInstance().getData().bphRecords + "");
        if (null != MainMeterDataParser.getInstance().getData() && MainMeterDataParser.getInstance().getData().bphRecords != 0) {
            readMultipleRecords(BphRecords);
        } else {
            upcomingStatus = Status.PARSE_MONEY;
            buffer = new StringBuilder();
            writeMoneyTransactions();
        }
    }

    private void parseBillingData(String packet) {
        Log.d("TAG", "parseBillingData");
        if (readRecords >= numOfRecords) {
            byte[] arr = Utils.hexStringToByteArray(packet);
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
//            Log.d("BphRecords", String.valueOf(buffer));
            ArrayList<Shared.BillingPeriodHistory> billingPeriodHistories = parser.SplitBPH(MainMeterDataParser.getInstance().getData(), Utils.hexStringToByteArray(buffer.toString()));
            if (billingPeriodHistories == null) {
                returnWithError("BillingPeriodHistoriesEx Failed");
                return;
            } else {
                meterData.setBillingPeriodHistories(billingPeriodHistories);
            }
            upcomingStatus = Status.PARSE_MONEY;
            buffer = new StringBuilder();
            writeMoneyTransactions();
        } else {
            byte[] arr = Utils.hexStringToByteArray(packet);
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
            upcomingStatus = Status.PARSE_BILLING;
            writeBillingHistory();
        }
    }

    private void writeMoneyTransactions() {
        Log.d("TAG", "writeMoneyTransactions");
        readRecords = 0;
        numOfRecords = -1;
        if (null != MainMeterDataParser.getInstance().getData() && MainMeterDataParser.getInstance().getData().moneyTransactionRecords != 0) {
            readMultipleRecords(MoneyRecords);
        } else {
            upcomingStatus = Status.PARSE_PROFILE;
            buffer = new StringBuilder();
            writeProfiles();
        }
    }

    private void parseMoneyTransactions(String packet) {
        Log.d("TAG", "parseMoneyTransactions");
        if (readRecords >= numOfRecords) {
            byte[] arr = Utils.hexStringToByteArray(packet);
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
//            Log.d("BphRecords", String.valueOf(buffer));
            ArrayList<Shared.MoneyTransaction> moneyTransactions = parser.SplitMoneyTransaction(Utils.hexStringToByteArray(buffer.toString()));
            if (moneyTransactions == null) {
                returnWithError("error: moneyTransactionsEx Failed");
                return;
            } else {
                meterData.setMoneyTransactions(moneyTransactions);
            }
            upcomingStatus = Status.PARSE_PROFILE;
            buffer = new StringBuilder();
            writeProfiles();
        } else {
//            Log.d("MoneyRecords :", readRecords + " == " + String.valueOf(packet));
            byte[] arr = Utils.hexStringToByteArray(packet);
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
            upcomingStatus = Status.PARSE_MONEY;
            writeMoneyTransactions();
        }
    }


    private void writeControlData() {
        readRecords = 0;
        numOfRecords = -1;
        if (!Utils.isNewControlData()) {
            returnWithSuccess();
        } else {
            Log.d("TAG", "writeControlData: ");
            Shared.DataRequestDataPacket RequestDataPacketPkt = new Shared.DataRequestDataPacket(newControlRawData, MainMeterDataParser.getInstance().getData().endianType);
            byte[] arrRequestData = RequestDataPacketPkt.ComposePacket();
            dataHandler(ConnectionManager.Companion.getInstance().sendWithLengthAsync(TextUtil.INSTANCE.toHexString(arrRequestData), 5, Common.TIME_OUT_500, 0));
        }
    }

    private void parseControlData(String packet) {
        Log.d("TAG", "parseControlData");
        Shared.NewControlData newControlData = parser.SplitNewControlData(MainMeterDataParser.getInstance().getData(), Utils.hexStringToByteArray(packet));
        if (newControlData == null) {
            returnWithError("error: Parse NewControlData: parsing fail");
            return;
        } else {
            meterData.setNewControlData(newControlData);
        }
        returnWithSuccess();
        //end and send output
    }

    private void writeProfiles() {
        Log.d("TAG", "writeProfiles");
        if (null != MainMeterDataParser.getInstance().getData() && MainMeterDataParser.getInstance().getData().profileRecords != 0) {
//            Log.d("Writing", "ProfilesCommand");
            readMultipleRecords(ProfileRecords);
        } else {
            upcomingStatus = Status.PARSE_METER_CONFIG;
            buffer = new StringBuilder();
            writeConfigData();
        }
    }

    private void parseProfiles(String packet) {
        Log.d("TAG", "parseProfiles");
        readRecords = 0;
        numOfRecords = -1;
        if (readRecords >= numOfRecords) {
            byte[] arr = Utils.hexStringToByteArray(packet);
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
//            Log.d("BphRecords", String.valueOf(buffer));
            ArrayList<Shared.ProfileRecord> profileRecords = parser.SplitProfileRecord(Utils.hexStringToByteArray(buffer.toString()));
            if (profileRecords == null) {
                returnWithError("error: ProfileEx Failed");
                return;
            } else {
                meterData.setProfileRecords(profileRecords);
            }
            upcomingStatus = Status.PARSE_METER_CONFIG;
            buffer = new StringBuilder();
            writeConfigData();
        } else {
//            Log.d("ProfileRecord :", readRecords + " == " + String.valueOf(packet));
            byte[] arr = Utils.hexStringToByteArray(packet);
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
            upcomingStatus = Status.PARSE_PROFILE;
            writeProfiles();
        }
    }

    private void writeConfigData() {
        Log.d("TAG", "writeConfigData");
        if (null != MainMeterDataParser.getInstance().getData() && MainMeterDataParser.getInstance().getData().configureMeterRecords != 0) {
//            Log.d("Writing", "ConfigDataCommand");
            readMultipleRecords(ConfigureRecords);
        } else {
            upcomingStatus = Status.PARSE_CONTROL;
            writeControlData();
        }

    }

    private void parseConfigurations(String packet) {
        Log.d("TAG", "parseConfigurations");
        readRecords = 0;
        numOfRecords = -1;
        if (readRecords >= numOfRecords) {
            byte[] arr = Utils.hexStringToByteArray(packet);
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
//            Log.d("parseConfigurations", String.valueOf(buffer));
            ArrayList<Shared.ConfigureMeter> configureMeters = parser.splitConfigureMeter(Utils.hexStringToByteArray(buffer.toString()));
            if (configureMeters == null) {
                returnWithError("error: ConfigureEx Failed");
                return;
            } else {
                meterData.setConfigureMeters(configureMeters);
            }
            upcomingStatus = Status.PARSE_CONTROL;
            writeControlData();
        } else {
            byte[] arr = Utils.hexStringToByteArray(packet);
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
            upcomingStatus = Status.PARSE_METER_CONFIG;
            writeConfigData();
        }
    }

    private void writeEventsRecords() {
        Log.d("TAG", "writeEventsRecords");
        readMultipleRecords(EventRecords);
    }


    private void parseEvents(String packet) {
        if (readRecords >= numOfRecords) {
//            Log.d("parseEvents", String.valueOf(buffer));
            ArrayList<Shared.Event> events = parser.splitEventLog(Utils.hexStringToByteArray(buffer.toString()));
            if (events == null) {
//                Log.d("TAG_ERROR:", "parseEventsEx");
            } else {
                meterData.setMeterEvents(events);
            }
            buffer = new StringBuilder();
            //Close Socket and send output
        } else {
//            Log.d("ConfigureRecord :", readRecords + " == " + packet);
            byte[] arr = Utils.hexStringToByteArray(packet);
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
            upcomingStatus = Status.PARSE_EVENT_DATA;
            writeEventsRecords();
        }
    }

    public Shared.DataNewDataListPacket.MeterType getMeterType() {
        return Shared.DataNewDataListPacket.meterType;
    }

    private void readMultipleRecords(Shared.UserRequestMeterReplyDataPackets userRequestMeterReplyDataPacket) {
        try {
            Log.d("TAG", "readMultipleRecords");
            int recordsCount = 0;
            int maxNumberOfRecords = 0;
            switch (userRequestMeterReplyDataPacket) {
                case BphRecords:
                    recordsCount = MainMeterDataParser.getInstance().getData().bphRecords;
                    maxNumberOfRecords = Shared.DataNewDataListPacket.MaxBphRecordsPerPacket;
                    break;

                case EventRecords:
                    recordsCount = MainMeterDataParser.getInstance().getData().eventRecords;
                    maxNumberOfRecords = Shared.DataNewDataListPacket.MaxEventRecordsPerPacket;
                    break;

                case MoneyRecords:
                    recordsCount = MainMeterDataParser.getInstance().getData().moneyTransactionRecords;
                    maxNumberOfRecords = Shared.DataNewDataListPacket.MaxMoneyTransactionRecordsPerPacket;
                    break;

                case ProfileRecords:
                    recordsCount = MainMeterDataParser.getInstance().getData().profileRecords;
                    maxNumberOfRecords = Shared.DataNewDataListPacket.MaxProfileRecordsPerPacket;
                    break;

                case ConfigureRecords:
                    recordsCount = MainMeterDataParser.getInstance().getData().configureMeterRecords;
                    maxNumberOfRecords = Shared.DataNewDataListPacket.MaxConfigureMeterRecordsPerPacket;
                    break;
                default:
                    recordsCount = 1;
                    maxNumberOfRecords = 1;
                    break;
            }
            if (userRequestMeterReplyDataPacket == ProfileRecords) {
                readRecords = recordsCount / maxNumberOfRecords + (recordsCount % maxNumberOfRecords > 0 ? 1 : 0) - 1;
            }

            numOfRecords = recordsCount / maxNumberOfRecords + (recordsCount % maxNumberOfRecords > 0 ? 1 : 0);

            if (readRecords <= recordsCount / maxNumberOfRecords + (recordsCount % maxNumberOfRecords > 0 ? 1 : 0)) {
                Shared.DataRequestDataPacket RequestDataPacketPkt = new Shared.DataRequestDataPacket(userRequestMeterReplyDataPacket, Shared.DataNewDataListPacket.endianType, readRecords * maxNumberOfRecords, (readRecords + 1) * maxNumberOfRecords > recordsCount ? recordsCount % maxNumberOfRecords : maxNumberOfRecords);
                byte[] arrRequestData = RequestDataPacketPkt.ComposePacket();
                readRecords++;
                Log.d("TAG", userRequestMeterReplyDataPacket.name());
                dataHandler(ConnectionManager.Companion.getInstance().sendWithLengthAsync(TextUtil.INSTANCE.toHexString(arrRequestData), 5, Common.TIME_OUT_500, 0));
            }
            boolean isFinishReadingMultipleRecords = readRecords >= ((recordsCount / maxNumberOfRecords) + (recordsCount % maxNumberOfRecords > 0 ? 1 : 0));
            if (isFinishReadingMultipleRecords) {
                readRecords = 0;
                numOfRecords = -1;
            }
        } catch (Exception exception) {
            readRecords = 0;
            numOfRecords = -1;
            Log.d("TAG", "readMultipleRecords: " + exception.getMessage());
        }
    }


    private void finalizeCurrentProcess() {
        timer1 = new Timer();
        timer1.schedule(new java.util.TimerTask() {
            @Override
            public void run() {
                if (!isBeforeFinished && isFirstTry) {
                    meterData.setResult("False");
                    meterData.setMessage("Time Out");
                    isBeforeFinished = true;
                    sho3a3MeterDataCallback.onGetSho3a3MeterData(meterData);
                    if (timer2 != null) {
                        timer2.cancel();
                    }
                }
            }
        }, 80000);
        timer2 = new Timer();
        timer2.schedule(new java.util.TimerTask() {
            @Override
            public void run() {
                if (!isBeforeFinished) {
                    meterData.setResult("False");
                    meterData.setMessage("Time Out");
                    isBeforeFinished = true;
                    sho3a3MeterDataCallback.onGetSho3a3MeterData(meterData);
                }
                if (timer1 != null) {
                    timer1.cancel();
                }
            }
        }, 120000);
    }


    private void returnWithError(String msg) {
        Log.d("TAG", "returnWithError: " + msg);
        meterData.setResult("False");
        meterData.setMessage(msg == null ? "Failed" : msg);
        this.isBeforeFinished = true;
        if (timer1 != null) {
            timer1.cancel();
        }
        if (timer2 != null) {
            timer2.cancel();
        }
        this.sho3a3MeterDataCallback.onGetSho3a3MeterData(meterData);
    }

    private void returnWithSuccess() {
        Log.d("TAG", "returnWithSuccess");
        if (timer1 != null) {
            timer1.cancel();
        }
        if (timer2 != null) {
            timer2.cancel();
        }
        meterData.setResult("True");
        isBeforeFinished = true;
        this.sho3a3MeterDataCallback.onGetSho3a3MeterData(meterData);
    }

    enum Status {
        INIT_CONNECTION_DATA_STEP1, INIT_CONNECTION_DATA_STEP2, PARSE_CONNECTION_DATA_STEP1, PARSE_CONNECTION_DATA_STEP2, METERING_DATA, PARSE_METERING, MONEY_DATA, PARSE_MONEY, BILLING_DATA, PARSE_BILLING, TARRIF_DATA, PARSE_TARRIF, PARSE_PROFILE, PROFILE_DATA, CONTROL_DATA, PARSE_CONTROL, TAMPER_DATA, PARSE_TAMPER, METER_CONFIG_DATA, PARSE_METER_CONFIG, EVENT_DATA, PARSE_EVENT_DATA
    }

    /**
     * Reset Bluetooth optical head communication parameters using BT_BAUD_R_9600_8_N_1
     * @return whether reset was successful
     */
    private boolean resetBluetoothOpticalHead() {
        try {
            if (!ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_R_9600_8_N_1)) {
                Log.e("TAG", "Failed to reset Bluetooth optical head parameters");
                return false;
            }
            currentBaudRate = BaudRate.BT_BAUD_R_9600_8_N_1;

            return true;
        } catch (Exception e) {
            Log.e("TAG", "Error occurred while resetting Bluetooth optical head parameters: " + e.getMessage());
            return false;
        }
    }

    /**
     * Set communication baud rate to BT_BAUD_N_4800_8_N_1
     * @return whether setting was successful
     */
    private boolean setCommunicationBaudRate() {
        try {
            if (!ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_N_4800_8_N_1)) {
                Log.e("TAG", "Failed to set communication baud rate");
                return false;
            }
            currentBaudRate = BaudRate.BT_BAUD_N_4800_8_N_1;
            return true;
        } catch (Exception e) {
            Log.e("TAG", "Error occurred while setting communication baud rate: " + e.getMessage());
            return false;
        }
    }

    /**
     * Set intermediate baud rate to BT_BAUD_M_4800_8_N_1
     * @return whether setting was successful
     */
    private boolean setIntermediateBaudRate() {
        try {
            if (!ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BT_BAUD_M_4800_8_N_1)) {
                Log.e("TAG", "Failed to set intermediate baud rate");
                return false;
            }
            currentBaudRate = BaudRate.BT_BAUD_M_4800_8_N_1;
            return true;
        } catch (Exception e) {
            Log.e("TAG", "Error occurred while setting intermediate baud rate: " + e.getMessage());
            return false;
        }
    }

    /**
     * Send IEC exit command mode (01 42 30 03 70)
     * @return whether command was sent successfully
     */
    private boolean sendIECExitCommand() {
        try {
            byte[] exitCommand = {0x01, 0x42, 0x30, 0x03, 0x70};
            ConnectionManager.Companion.getInstance().sendAsync(TextUtil.INSTANCE.toHexString(exitCommand), 0, Common.TIME_OUT_300, Common.TIME_OUT_200);
            return true;
        } catch (Exception e) {
            Log.e("TAG", "Error occurred while sending IEC exit command: " + e.getMessage());
            return false;
        }
    }

}
