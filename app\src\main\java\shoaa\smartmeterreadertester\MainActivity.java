package shoaa.smartmeterreadertester;

import static android.os.Build.VERSION.SDK_INT;
import static shoaa.common.Utils.writeStringAsFile;

import android.Manifest;
import android.app.Activity;
import android.app.ActivityManager;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.FileProvider;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;

import java.io.File;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;

import pub.devrel.easypermissions.AfterPermissionGranted;
import pub.devrel.easypermissions.EasyPermissions;
import shoaa.connectionmanager.ConnectionManager;
import shoaa.connectionmanager.ConnectionNotifier;
import shoaa.connectionmanager.DisplayDevice;
import shoaa.connectionmanager.bluetooth.SerialService;
import shoaa.electrometerreader.ElectrometerResponse;
import shoaa.esmcreader.EsmcResponse;
import shoaa.globalreader.GlobalResponse;
import shoaa.globalreader.GlobalVersion;
import shoaa.gpireader.GpiResponse;
import shoaa.hay2areader.Hay2aResponse;
import shoaa.iskrareader.IskraResponse;
import shoaa.maasarareader.MaasaraResponse;
import shoaa.smartmeterreader.MeterCo;
import shoaa.smartmeterreader.MeterReader;
import shoaa.smartmeterreader.ReadingResponse;
import shoaa.smartmeterreader.ReadingResult;


public class MainActivity extends Activity implements EasyPermissions.PermissionCallbacks {
    public static final int MY_PERMISSIONS = 120;
    TextView txtData;
    Button btnGetData;
    ReadingResponse data = null;
    String[] metersCo = {"جلوبال", "المصريه", "اسكرا", "الكتروميتر", "المعصره", "GPI" , "الهيئة العربية"};
    String[] globalVersions = {Calendar.getInstance().get(Calendar.YEAR) + "←2019", "2018←2013", "2012←2008"};
    //    String[] metersCo = {"جلوبال"};
    private ArrayAdapter<DisplayDevice> listAdapter = null;

    MeterCo getMeterCo(int index) {
        switch (index) {
            case 0:
                return MeterCo.GLOBAL;
            case 1:
                return MeterCo.ESMC;
            case 2:
                return MeterCo.ISKRA;
            case 3:
                return MeterCo.ELECTROMETER;
            case 4:
                return MeterCo.MAASARA;
            case 5:
                return MeterCo.GPI;
            case 6:
                return MeterCo.Hay2a;
            default:
                return null;
        }
    }

    private long getTime() throws Exception {
        String url = "https://time.is/Unix_time_now";
        Document doc = Jsoup.parse(new URL(url).openStream(), "UTF-8", url);
        String[] tags = new String[]{
                "div[id=time_section]",
                "div[id=clock0_bg]"
        };
        Elements elements = doc.select(tags[0]);
        for (String tag : tags) {
            elements = elements.select(tag);
        }
        return Long.parseLong(elements.text());
    }

    private String getDate(long time) {
        Calendar cal = Calendar.getInstance(Locale.ENGLISH);
        cal.setTimeInMillis(time * 1000);
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy hh:mm a", Locale.ENGLISH);

        return dateFormat.format(cal.getTime());
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        new Thread(() -> {
//            try {
//                long time = getTime();
//                String date = getDate(time);
//                if (date.isEmpty() || time == 0)
//                    throw new Exception();
//                SimpleDateFormat curFormater = new SimpleDateFormat("dd/MM/yyyy hh:mm a", Locale.US);
//                try {
//                    Date timestamp = curFormater.parse(date);
//                    Date dateObj = curFormater.parse("01/08/2024 00:00 am");
//                    if (timestamp == null || timestamp.after(dateObj)) {
//                        System.exit(0);
//                    }
//                } catch (ParseException e) {
//                    System.exit(0);
//                }
//            } catch (Exception e) {
//                SimpleDateFormat curFormater = new SimpleDateFormat("dd/MM/yyyy", Locale.US);
//                try {
//                    Calendar timestamp = Calendar.getInstance();
//                    Date dateObj = curFormater.parse("01/08/2024");
//                    if (timestamp.getTime().after(dateObj)) {
//                        System.exit(0);
//                    }
//                } catch (ParseException ex) {
//                    System.exit(0);
//                }
//            }
//        }).start();
        setContentView(R.layout.activity_main);
        Spinner spinner = findViewById(R.id.spinner);
        Spinner spinner_meterCo = findViewById(R.id.spinner_meterCo);
        Spinner spinner_GlobalVersions = findViewById(R.id.spinner_globalVersion);
        btnGetData = findViewById(R.id.btn_getData);
        txtData = findViewById(R.id.txt_data);
        ArrayAdapter<String> spinnerArrayAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, metersCo);
        ArrayAdapter<String> globalArrayAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, globalVersions);
        spinnerArrayAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item); // The drop down view
        globalArrayAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item); // The drop down view
        spinner_meterCo.setAdapter(spinnerArrayAdapter);
        spinner_GlobalVersions.setAdapter(globalArrayAdapter);
        spinner_meterCo.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position == 0) {
                    spinner_GlobalVersions.setVisibility(View.VISIBLE);
                } else {
                    spinner_GlobalVersions.setVisibility(View.GONE);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
        spinner_meterCo.setSelection(0);
        findViewById(R.id.btn_send).setOnClickListener(view -> shareMultiple());

        listAdapter = new ArrayAdapter<DisplayDevice>(this, android.R.layout.simple_spinner_dropdown_item, ConnectionManager.Companion.getInstance().getConnectedDisplayDeviceList()) {
            @Override
            public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
                DisplayDevice device = ConnectionManager.Companion.getInstance().getConnectedDisplayDeviceList().get(position);
                View view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                TextView text1 = view.findViewById(R.id.text1);
                TextView text2 = view.findViewById(R.id.text2);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (ActivityCompat.checkSelfPermission(getApplicationContext(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                        requestPermissions(new String[]{Manifest.permission.BLUETOOTH_CONNECT}, 0);
                        return view;
                    }
                }
                text1.setText(device.getName());
                text2.setText(device.getAddress());
                return view;
            }

            @NonNull
            @Override
            public View getView(int position, View view, @NonNull ViewGroup parent) {
                DisplayDevice device = ConnectionManager.Companion.getInstance().getConnectedDisplayDeviceList().get(position);
                if (view == null)
                    view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                TextView text1 = view.findViewById(R.id.text1);
                TextView text2 = view.findViewById(R.id.text2);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (ActivityCompat.checkSelfPermission(getApplicationContext(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                        requestPermissions(new String[]{Manifest.permission.BLUETOOTH_CONNECT}, 0);
                        return view;
                    }
                }
                text1.setText(device.getName());
                text2.setText(device.getAddress());
                return view;
            }
        };

        spinner.setAdapter(listAdapter);
        NumberProgressBar numberProgressBar = findViewById(R.id.progress_circle);
        btnGetData.setOnClickListener(v -> {
            DisplayDevice dev = listAdapter.getItem(spinner.getSelectedItemPosition());
            if (dev != null) {
                ConnectionManager.Companion.getInstance().setDevice(dev);
            }
            data = null;
            File outDir = new File(this.getFilesDir(), "dataDir");
            if (outDir.exists() && outDir.canRead()) {
                for (File frame : outDir.listFiles()) {
                    frame.delete();
                }
                outDir.delete();
            }
            outDir.mkdirs();
            txtData.setText("");
            AtomicInteger tryCount = new AtomicInteger();
            numberProgressBar.setVisibility(View.VISIBLE);
            new Thread(() -> {
                runOnUiThread(() -> btnGetData.setEnabled(false));
                while ((data == null || (data.readingResult == ReadingResult.CAN_NOT_CONNECT || data.readingResult == ReadingResult.CAN_NOT_SET_BaudRate)) && tryCount.get() < 3) {
                    tryCount.getAndIncrement();
                    GlobalVersion globalVersion = getGlobalVersion(spinner_meterCo, spinner_GlobalVersions);
                    data = MeterReader.read(getApplicationContext(), getMeterCo(spinner_meterCo.getSelectedItemPosition()), globalVersion, progress -> {
                        Log.d("Reading Meter", "Progress: " + progress + "%");
                        numberProgressBar.setProgress(progress);
                        numberProgressBar.setMax(100);
                    });
                }
//                bluetoothHandler.disconnect();

                runOnUiThread(() -> {
                    numberProgressBar.setVisibility(View.GONE);
                    numberProgressBar.setProgress(0);
                    if (data != null) {
                        if (data.readingResult == ReadingResult.SUCCESS) {
//                            txtData.setText(data.toString());
                            writeStringAsFile(MainActivity.this, data.toFileFormate(), "response.txt");

                            if (data instanceof GlobalResponse) {
                                String txt = "نوع العداد: " + (((GlobalResponse) data).getMeterType().equals("Single") ? "احادي" :
                                        ((GlobalResponse) data).getMeterType().equals("3Phase") ? "ثلاثي" : "غير معروف") + "\n";
                                txt += "رقم العداد: " + ((GlobalResponse) data).getMeterId() + "\n";
                                txt += "الرصيد الحالي : " + ((GlobalResponse) data).getRemainMoney() + "\n";
                                txt += "الديون : " + ((GlobalResponse) data).getDeon() + "\n";
                                txt += "اجمالي الاستهلاك : " + ((GlobalResponse) data).getTotalConsum() + "\n";
                                txt += "استهلاك الشهر الحالي : " + ((GlobalResponse) data).getCurrentMonthConsumption() + "\n";
                                txt += "الوقت و التاريخ : " + ((GlobalResponse) data).getDate() + "\n";
                                txt += "الشريحه الحاليه : " + ((GlobalResponse) data).getSliceNo() + "\n";
                                txt += "حاله البطاريه : " + (((GlobalResponse) data).getBatteryStatus().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الريلاي : " + (((GlobalResponse) data).getRelayStatus().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله  : " + ((GlobalResponse) data).getTempresList() + "\n";
                                txtData.setText(txt);
                            } else if (data instanceof ElectrometerResponse) {
                                String txt = "نوع العداد: " + (((ElectrometerResponse) data).getMeterType().equals("Single") ? "احادي" :
                                        ((ElectrometerResponse) data).getMeterType().equals("3Phase") ? "ثلاثي" : "غير معروف") + "\n";
                                txt += "رقم العداد: " + ((ElectrometerResponse) data).getMeterId() + "\n";
                                txt += "الرصيد الحالي : " + ((ElectrometerResponse) data).getRemainingCreditMoney() + "\n";
                                txt += "الديون : " + ((ElectrometerResponse) data).getDebts() + "\n";
                                txt += "اجمالي الاستهلاك : " + ((ElectrometerResponse) data).getTotalConsumptionKw() + "\n";
                                txt += "استهلاك الشهر الحالي : " + ((ElectrometerResponse) data).getCurrentMonthConsumptionKW() + "\n";
                                txt += "الوقت و التاريخ : " + ((ElectrometerResponse) data).getMeterDateAndTime() + "\n";
                                txt += "الشريحه الحاليه : " + ((ElectrometerResponse) data).getCurrentTarrifInstalling() + "\n";
                                txt += "حاله البطاريه : " + (((ElectrometerResponse) data).getBatteryStatus().equals("B0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الريلاي : " + (((ElectrometerResponse) data).getRelayStatus().equals("R0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الغطاء الامامي : " + (((ElectrometerResponse) data).getTopCoverStatus().equals("T0_0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الغطاء الجانبي : " + (((ElectrometerResponse) data).getSideCoverStatus().equals("S0_0") ? "سلبم" : "غير سليم") + "\n";
                                txtData.setText(txt);
                            } else if (data instanceof IskraResponse) {
                                String txt = "نوع العداد: " + (((IskraResponse) data).getMeterType().equals("Single") ? "احادي" :
                                        ((IskraResponse) data).getMeterType().equals("3Phase") ? "ثلاثي" : "غير معروف") + "\n";
                                txt += "رقم العداد: " + ((IskraResponse) data).getMeterId() + "\n";
                                txt += "الرصيد الحالي : " + ((IskraResponse) data).getRemainingCreditMoney() + "\n";
                                txt += "الديون : " + ((IskraResponse) data).getDebts() + "\n";
                                txt += "اجمالي الاستهلاك : " + ((IskraResponse) data).getTotalConsumptionKw() + "\n";
                                txt += "استهلاك الشهر الحالي : " + ((IskraResponse) data).getCurrentMonthConsumptionKW() + "\n";
                                txt += "الوقت و التاريخ : " + ((IskraResponse) data).getMeterDateAndTime() + "\n";
                                txt += "الشريحه الحاليه : " + ((IskraResponse) data).getCurrentTarrifInstalling() + "\n";
                                txt += "حاله البطاريه : " + (((IskraResponse) data).getBatteryStatus().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الريلاي : " + (((IskraResponse) data).getRelayStatus().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الغطاء الامامي : " + (((IskraResponse) data).getTopCoverStatus().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الغطاء الجانبي : " + (((IskraResponse) data).getSideCoverStatus().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txtData.setText(txt);
                            } else if (data instanceof EsmcResponse) {
                                String txt = "نوع العداد: " + (((EsmcResponse) data).getMeterType().equals("Single") ? "احادي" :
                                        ((EsmcResponse) data).getMeterType().equals("3Phase") ? "ثلاثي" : "غير معروف") + "\n";
                                txt += "رقم العداد: " + ((EsmcResponse) data).getMeterId() + "\n";
                                txt += "الرصيد الحالي : " + ((EsmcResponse) data).getRemainingCreditMoney() + "\n";
                                txt += "الديون : " + ((EsmcResponse) data).getDebts() + "\n";
                                txt += "اجمالي الاستهلاك : " + ((EsmcResponse) data).getTotalConsumptionKw() + "\n";
                                txt += "استهلاك الشهر الحالي : " + ((EsmcResponse) data).getCurrentMonthConsumptionKW() + "\n";
                                txt += "الوقت و التاريخ : " + ((EsmcResponse) data).getMeterDateAndTime() + "\n";
                                txt += "الشريحه الحاليه : " + ((EsmcResponse) data).getCurrentTarrifInstalling() + "\n";
                                txt += "حاله البطاريه : " + (((EsmcResponse) data).getBatteryStatus().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الريلاي : " + (((EsmcResponse) data).getRelayStatus().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الغطاء الامامي : " + (((EsmcResponse) data).getTopCoverStatus().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الغطاء الجانبي : " + (((EsmcResponse) data).getSideCoverStatus().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txtData.setText(txt);
                            } else if (data instanceof MaasaraResponse) {
                                String txt = "نوع العداد: " + (((MaasaraResponse) data).getMeterType().equals("Single") ? "احادي" :
                                        ((MaasaraResponse) data).getMeterType().equals("3Phase") ? "ثلاثي" : "غير معروف") + "\n";
//                                txt += "رقم العداد: " + ((MaasaraResponse) data).getMeterId() + "\n";
//                                txt += "الرصيد الحالي : " + ((MaasaraResponse) data).getRemainingCreditMoney() + "\n";
//                                txt += "الديون : " + ((MaasaraResponse) data).getDebts() + "\n";
//                                txt += "اجمالي الاستهلاك : " + ((MaasaraResponse) data).getTotalConsumptionKw() + "\n";
//                                txt += "استهلاك الشهر الحالي : " + ((MaasaraResponse) data).getCurrentMonthConsumptionKW() + "\n";
//                                txt += "الوقت و التاريخ : " + ((MaasaraResponse) data).getMeterDateAndTime() + "\n";
//                                txt += "الشريحه الحاليه : " + ((MaasaraResponse) data).getCurrentTarrifInstalling() + "\n";
//                                txt += "حاله البطاريه : " + (((MaasaraResponse) data).getBatteryStatus().equals("0") ? "سلبم" : "غير سليم") + "\n";
//                                txt += "حاله الريلاي : " + (((MaasaraResponse) data).getRelayStatus().equals("0") ? "سلبم" : "غير سليم") + "\n";
//                                txt += "حاله الغطاء الامامي : " + (((MaasaraResponse) data).getTopCoverStatus().equals("0") ? "سلبم" : "غير سليم") + "\n";
//                                txt += "حاله الغطاء الجانبي : " + (((MaasaraResponse) data).getSideCoverStatus().equals("0") ? "سلبم" : "غير سليم") + "\n";

                                txt += "رقم العداد: " + ((MaasaraResponse) data).getMeter_ID() + "\n";
                                txt += "الرصيد الحالي : " + ((MaasaraResponse) data).getRemaining_credit_mony() + "\n";
                                txt += "الديون : " + ((MaasaraResponse) data).getDebts() + "\n";
                                txt += "اجمالي الاستهلاك : " + ((MaasaraResponse) data).getTotal_consumption_kw() + "\n";
                                txt += "استهلاك الشهر الحالي : " + ((MaasaraResponse) data).getCurrent_month_consumption_KW() + "\n";
                                txt += "الوقت و التاريخ : " + ((MaasaraResponse) data).getMeter_Date() + "\t" + ((MaasaraResponse) data).getMeter_Time() + "\n";
                                txt += "الشريحه الحاليه : " + ((MaasaraResponse) data).getItem_13_Current_tarrif_installing() + "\n";
                                txt += "حاله البطاريه : " + (((MaasaraResponse) data).getBattery_status().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الريلاي : " + (((MaasaraResponse) data).getRelay_status().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الغطاء الامامي : " + (((MaasaraResponse) data).getTop_cover_status().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الغطاء الجانبي : " + (((MaasaraResponse) data).getSide_cover_status().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txtData.setText(txt);
                            } else if (data instanceof GpiResponse) {
                                String txt = "نوع العداد: " + (((GpiResponse) data).getMeterType().equals("Single") ? "احادي" :
                                        ((GpiResponse) data).getMeterType().equals("3Phase") ? "ثلاثي" : "غير معروف") + "\n";
                                txt += "رقم العداد: " + ((GpiResponse) data).getMeter_ID() + "\n";
                                txt += "الرصيد الحالي : " + ((GpiResponse) data).getRemaining_credit_mony() + "\n";
                                txt += "الديون : " + ((GpiResponse) data).getDebts() + "\n";
                                txt += "اجمالي الاستهلاك : " + ((GpiResponse) data).getTotal_consumption_kw() + "\n";
                                txt += "استهلاك الشهر الحالي : " + ((GpiResponse) data).getCurrent_month_consumption_KW() + "\n";
                                txt += "الوقت و التاريخ : " + ((GpiResponse) data).getMeter_Date() + "\t" + ((GpiResponse) data).getMeter_Time() + "\n";
                                txt += "الشريحه الحاليه : " + ((GpiResponse) data).getItem_13_Current_tarrif_installing() + "\n";
                                txt += "حاله البطاريه : " + (((GpiResponse) data).getBattery_status().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الريلاي : " + (((GpiResponse) data).getRelay_status().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الغطاء الامامي : " + (((GpiResponse) data).getTop_cover_status().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الغطاء الجانبي : " + (((GpiResponse) data).getSide_cover_status().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txtData.setText(txt);
                            }else if (data instanceof Hay2aResponse) {
                                String txt = "نوع العداد: " + (((Hay2aResponse) data).getMeterType().equals("Single") ? "احادي" :
                                        ((Hay2aResponse) data).getMeterType().equals("3Phase") ? "ثلاثي" : "غير معروف") + "\n";
                                txt += "رقم العداد: " + ((Hay2aResponse) data).getMeter_ID() + "\n";
                                txt += "الرصيد الحالي : " + ((Hay2aResponse) data).getRemaining_credit_mony() + "\n";
                                txt += "الديون : " + ((Hay2aResponse) data).getDebts() + "\n";
                                txt += "اجمالي الاستهلاك : " + ((Hay2aResponse) data).getTotal_consumption_kw() + "\n";
                                txt += "استهلاك الشهر الحالي : " + ((Hay2aResponse) data).getCurrent_month_consumption_KW() + "\n";
                                txt += "الوقت و التاريخ : " + ((Hay2aResponse) data).getMeter_Date() + "\t" + ((Hay2aResponse) data).getMeter_Time() + "\n";
                                txt += "الشريحه الحاليه : " + ((Hay2aResponse) data).getItem_13_Current_tarrif_installing() + "\n";
                                txt += "حاله البطاريه : " + (((Hay2aResponse) data).getBattery_status().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الريلاي : " + (((Hay2aResponse) data).getRelay_status().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الغطاء الامامي : " + (((Hay2aResponse) data).getTop_cover_status().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txt += "حاله الغطاء الجانبي : " + (((Hay2aResponse) data).getSide_cover_status().equals("0") ? "سلبم" : "غير سليم") + "\n";
                                txtData.setText(txt);
                            }
                        } else {
                            switch (data.readingResult) {
                                case CAN_NOT_CONNECT:
                                    Toast.makeText(this, "غير قادر علي الاتصال", Toast.LENGTH_LONG).show();
                                    break;
                                case CAN_NOT_GET_DATA:
                                    Toast.makeText(this, "غير قادر علي القراءه", Toast.LENGTH_LONG).show();
                                    break;
                                case CAN_NOT_PARSE_DATA:
                                    Toast.makeText(this, "غير قادر علي الترجمه", Toast.LENGTH_LONG).show();
                                    break;
                                case CAN_NOT_SET_BaudRate:
                                    Toast.makeText(this, "خطا سرعه البيانات", Toast.LENGTH_LONG).show();
                                    break;
                                case JSON_ERROR:
                                    Toast.makeText(this, "JSON خطا", Toast.LENGTH_LONG).show();
                                    break;
                                case OTHER:
                                    Toast.makeText(this, data.message, Toast.LENGTH_LONG).show();
                                    break;
                            }
                        }
                    }
                    btnGetData.setEnabled(true);
                });
            }).start();
        });
    }

    @Nullable
    private static GlobalVersion getGlobalVersion(Spinner spinner_meterCo, Spinner spinner_GlobalVersions) {
        GlobalVersion globalVersion = null;
        if (spinner_meterCo.getSelectedItemPosition() == 0) {
            if (spinner_GlobalVersions.getSelectedItemPosition() == 0) {
                globalVersion = GlobalVersion.VERSION_2019;
            } else if (spinner_GlobalVersions.getSelectedItemPosition() == 1) {
                globalVersion = GlobalVersion.VERSION_2013;
            } else {
                globalVersion = GlobalVersion.VERSION_2008;
            }
        }
        return globalVersion;
    }


    public void shareMultiple() {
        File outDir = new File(this.getFilesDir(), "dataDir");
        if (!outDir.exists() || outDir.listFiles().length == 0)
            return;
        List<File> files = new ArrayList<>();
        for (File outFile : outDir.listFiles())
            files.add(outFile);
        ArrayList<Uri> uris = new ArrayList<>();
        for (File file : files) {
            uris.add(FileProvider.getUriForFile(this, this.getApplication().getPackageName() + ".fileprovider", file));
        }
        final Intent intent = new Intent(Intent.ACTION_SEND_MULTIPLE);
        intent.setType("*/*");
        intent.putParcelableArrayListExtra(Intent.EXTRA_STREAM, uris);
        this.startActivity(Intent.createChooser(intent, "ارسال النسخة الاحتياطية"));
    }


    @AfterPermissionGranted(MY_PERMISSIONS)
    private void checkPermissions() {
        ArrayList<String> permsList = new ArrayList<>();
        if (SDK_INT < Build.VERSION_CODES.S) {
            permsList.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }
        permsList.add(Manifest.permission.CAMERA);
        permsList.add(Manifest.permission.ACCESS_FINE_LOCATION);
        permsList.add(Manifest.permission.ACCESS_COARSE_LOCATION);
        if (SDK_INT >= Build.VERSION_CODES.P) {
            permsList.add(Manifest.permission.FOREGROUND_SERVICE);
        }
        if (SDK_INT >= Build.VERSION_CODES.S) {
            permsList.add(Manifest.permission.BLUETOOTH_CONNECT);
            permsList.add(Manifest.permission.BLUETOOTH_SCAN);
        } else {
            permsList.add(Manifest.permission.BLUETOOTH);
            permsList.add(Manifest.permission.BLUETOOTH_ADMIN);
        }
        if (SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permsList.add(Manifest.permission.POST_NOTIFICATIONS);
        }
        if (SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            permsList.add(Manifest.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE);
        }
        String[] perms = permsList.toArray(new String[0]);
        if (!EasyPermissions.hasPermissions(this, perms)) {
            EasyPermissions.requestPermissions(this, "يجب الموافقة علي الاذونات",
                    MY_PERMISSIONS, perms);
        } else {
            // Use this check to determine whether Bluetooth classic is supported on the device.
            if (!getPackageManager().hasSystemFeature(PackageManager.FEATURE_BLUETOOTH)) {
                Toast.makeText(this, "bluetooth_not_supported", Toast.LENGTH_SHORT).show();
//                finish();
            }

            ConnectionManager.Companion.getInstance().init(this, true, new ConnectionNotifier() {
                @Override
                public void notifyConnection() {
                    runOnUiThread(() -> listAdapter.notifyDataSetChanged());
                }
            });
        }
    }


    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this);
    }


    @Override
    public void onPermissionsGranted(int requestCode, @NonNull List<String> perms) {
        ArrayList<String> permsList = new ArrayList<>();
        if (SDK_INT < Build.VERSION_CODES.S) {
            permsList.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }
        permsList.add(Manifest.permission.CAMERA);
        permsList.add(Manifest.permission.ACCESS_FINE_LOCATION);
        permsList.add(Manifest.permission.ACCESS_COARSE_LOCATION);
        if (SDK_INT >= Build.VERSION_CODES.P) {
            permsList.add(Manifest.permission.FOREGROUND_SERVICE);
        }
        if (SDK_INT >= Build.VERSION_CODES.S) {
            permsList.add(Manifest.permission.BLUETOOTH_CONNECT);
            permsList.add(Manifest.permission.BLUETOOTH_SCAN);
        } else {
            permsList.add(Manifest.permission.BLUETOOTH);
            permsList.add(Manifest.permission.BLUETOOTH_ADMIN);
        }
        if (SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permsList.add(Manifest.permission.POST_NOTIFICATIONS);
        }
        if (SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            permsList.add(Manifest.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE);
        }
        String[] perms1 = permsList.toArray(new String[0]);
        if (!EasyPermissions.hasPermissions(this, perms1)) {
//            System.exit(0);
            ((ActivityManager) getSystemService(ACTIVITY_SERVICE)).clearApplicationUserData();
            checkPermissions();
        }
    }

    @Override
    public void onPermissionsDenied(int requestCode, @NonNull List<String> perms) {
        System.exit(0);
    }

    @Override
    public void onDestroy() {
        ConnectionManager.Companion.getInstance().disconnect();
        stopService(new Intent(this, SerialService.class));
        super.onDestroy();
    }

    @Override
    public void onStart() {
        super.onStart();
        checkPermissions();
    }

    @Override
    public void onStop() {
        if (!isChangingConfigurations())
            ConnectionManager.Companion.getInstance().deAttachBluetoothService();
        super.onStop();
    }
}