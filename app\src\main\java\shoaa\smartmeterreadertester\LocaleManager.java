package shoaa.smartmeterreadertester;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;

import java.util.Locale;

public class LocaleManager {
    public static Context updateResources(Context context, String language) {
        Locale locale = new Locale(language);
        Locale.setDefault(locale);
        Resources res = context.getResources();
        Configuration config = new Configuration(res.getConfiguration());
        config.locale = locale;
        config.setLocale(locale);
        res.updateConfiguration(config, res.getDisplayMetrics());

        config.setLocale(locale);
        context = context.createConfigurationContext(config);
        return context;
    }
}