plugins {
    id 'com.android.library'
}


android {
    compileSdk 34
    buildToolsVersion "33.0.1"

    defaultConfig {
        minSdkVersion 24
        targetSdkVersion 34
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    namespace 'shoaa.smartmeterreader'
}
dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'com.google.guava:guava:32.1.3-jre'
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'org.apache.commons:commons-lang3:3.12.0'
    
    implementation files('../app/libs/EsmcProtocol_v2.jar')
    implementation files('../app/libs/dlms-maasra.jar')
    implementation(files('../app/libs/dlms-gpi.jar'))


    implementation 'org.bouncycastle:bcprov-jdk18on:1.71'
    implementation 'javax.xml.bind:jaxb-api:2.3.1'
    implementation 'com.beanit:asn1bean:1.13.0'
    implementation 'org.openmuc:jrxtx:1.0.1'
    implementation 'xerces:xercesImpl:2.12.0'
    implementation files('../app/libs/hayka/jdlms-1.7.3.jar')

    implementation project(path: ':ElectrometerParser')
    implementation project(path: ':ConnectionManager')
    implementation project(path: ':Common')
}