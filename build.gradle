// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        agp_version = '7.3.1'
    }
    repositories {
        maven { url uri('local-repo') } // 本地仓库，优先级最高
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        google()
        mavenCentral()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:$agp_version"
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.22'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}