<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON>rad<PERSON> or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.android.tools.build</groupId>
  <artifactId>gradle</artifactId>
  <version>8.3.2</version>
  <dependencies>
    <dependency>
      <groupId>com.android.tools.build</groupId>
      <artifactId>builder</artifactId>
      <version>8.3.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.build</groupId>
      <artifactId>builder-model</artifactId>
      <version>8.3.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.build</groupId>
      <artifactId>gradle-api</artifactId>
      <version>8.3.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.build</groupId>
      <artifactId>gradle-settings-api</artifactId>
      <version>8.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools</groupId>
      <artifactId>sdk-common</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools</groupId>
      <artifactId>sdklib</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools</groupId>
      <artifactId>repository</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.ddms</groupId>
      <artifactId>ddmlib</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.build</groupId>
      <artifactId>aapt2-proto</artifactId>
      <version>8.3.2-10880808</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.build</groupId>
      <artifactId>aaptcompiler</artifactId>
      <version>8.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.analytics-library</groupId>
      <artifactId>crash</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.analytics-library</groupId>
      <artifactId>shared</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.lint</groupId>
      <artifactId>lint-model</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.lint</groupId>
      <artifactId>lint-typedef-remover</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-compiler-common</artifactId>
      <version>8.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.databinding</groupId>
      <artifactId>databinding-common</artifactId>
      <version>8.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.databinding</groupId>
      <artifactId>baseLibrary</artifactId>
      <version>8.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.build</groupId>
      <artifactId>builder-test-api</artifactId>
      <version>8.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.layoutlib</groupId>
      <artifactId>layoutlib-api</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.utp</groupId>
      <artifactId>android-device-provider-ddmlib-proto</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.utp</groupId>
      <artifactId>android-device-provider-gradle-proto</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.utp</groupId>
      <artifactId>android-test-plugin-host-additional-test-output-proto</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.utp</groupId>
      <artifactId>android-test-plugin-host-coverage-proto</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.utp</groupId>
      <artifactId>android-test-plugin-host-emulator-control-proto</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.utp</groupId>
      <artifactId>android-test-plugin-host-logcat-proto</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.utp</groupId>
      <artifactId>android-test-plugin-host-apk-installer-proto</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.utp</groupId>
      <artifactId>android-test-plugin-host-retention-proto</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.utp</groupId>
      <artifactId>android-test-plugin-result-listener-gradle-proto</artifactId>
      <version>31.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib-jdk8</artifactId>
      <version>1.9.20</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.build</groupId>
      <artifactId>transform-api</artifactId>
      <version>2.0.0-deprecated-use-gradle-api</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpmime</artifactId>
      <version>4.5.6</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.13.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm</artifactId>
      <version>9.6</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm-analysis</artifactId>
      <version>9.6</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm-commons</artifactId>
      <version>9.6</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm-util</artifactId>
      <version>9.6</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk15on</artifactId>
      <version>1.67</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.glassfish.jaxb</groupId>
      <artifactId>jaxb-runtime</artifactId>
      <version>2.3.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>net.sf.jopt-simple</groupId>
      <artifactId>jopt-simple</artifactId>
      <version>4.9</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.build</groupId>
      <artifactId>bundletool</artifactId>
      <version>1.15.6</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.build.jetifier</groupId>
      <artifactId>jetifier-core</artifactId>
      <version>1.0.0-beta10</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.build.jetifier</groupId>
      <artifactId>jetifier-processor</artifactId>
      <version>1.0.0-beta10</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.squareup</groupId>
      <artifactId>javapoet</artifactId>
      <version>1.10.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>3.22.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java-util</artifactId>
      <version>3.22.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.10.1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-core</artifactId>
      <version>1.57.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-netty</artifactId>
      <version>1.57.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-protobuf</artifactId>
      <version>1.57.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-stub</artifactId>
      <version>1.57.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.crypto.tink</groupId>
      <artifactId>tink</artifactId>
      <version>1.7.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.testing.platform</groupId>
      <artifactId>core-proto</artifactId>
      <version>0.0.9-alpha02</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>net.sf.kxml</groupId>
      <artifactId>kxml2</artifactId>
      <version>2.3.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.flatbuffers</groupId>
      <artifactId>flatbuffers-java</artifactId>
      <version>1.12.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.tensorflow</groupId>
      <artifactId>tensorflow-lite-metadata</artifactId>
      <version>0.1.0-rc2</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
  <description>Gradle plug-in to build Android applications.</description>
  <url>https://developer.android.com/studio/build</url>
  <name>com.android.tools.build.gradle</name>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>The Android Open Source Project</name>
    </developer>
  </developers>
  <scm>
    <connection>git://android.googlesource.com/platform/tools/base.git</connection>
    <url>https://android.googlesource.com/platform/tools/base</url>
  </scm>
</project>
